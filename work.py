import logging
import as<PERSON><PERSON>
import os # Import the os module
from telegram import Rep<PERSON><PERSON><PERSON>boardMarkup, ReplyKeyboardRemove, Update
from telegram.ext import (
    Application,
    CommandHandler,
    ContextTypes,
    ConversationHandler,
    MessageHandler,
    filters,
)

# Enable logging
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s", level=logging.INFO
)
logger = logging.getLogger(__name__)

# --- Configuration ---
# IMPORTANT: Set these environment variables before running the script:
# TELEGRAM_BOT_TOKEN: Your bot's actual token from BotFather.
# TELEGRAM_CHAT_ID: The specific chat ID where you want the bot to proactively message.
# You can get your Chat ID by sending a message to your bot and then visiting
# https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates
# Look for "chat": {"id": <YOUR_CHAT_ID>, ...}
YOUR_BOT_TOKEN = os.getenv("TELEGRAM_BOT_TOKEN")
TARGET_CHAT_ID = os.getenv("TELEGRAM_CHAT_ID")

# Ensure environment variables are set
if not YOUR_BOT_TOKEN:
    logger.error("TELEGRAM_BOT_TOKEN environment variable not set.")
    exit(1)
if not TARGET_CHAT_ID:
    logger.error("TELEGRAM_CHAT_ID environment variable not set.")
    exit(1)

# Define conversation states
MAIN_MENU, ROBO_STRATEGIES_MENU, OWN_STRATEGIES_START, GET_TICKER, GET_DIVIDEND_REINVEST, ADD_ANOTHER_STRATEGY = range(6)

# --- Main Menu Handlers ---

async def send_main_menu(chat_id: str, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Sends the main menu options to a specific chat ID."""
    reply_keyboard = [["Default"], ["Robo Strategies"], ["Own Strategies"]]
    await context.bot.send_message(
        chat_id=chat_id,
        text="Choose one of the following options:",
        reply_markup=ReplyKeyboardMarkup(
            reply_keyboard, one_time_keyboard=True, input_field_placeholder="Select an option"
        ),
    )

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """Starts the conversation and presents the main menu options."""
    # This handler is now primarily for restarting the conversation if needed,
    # or for users who message the bot after it has already sent its initial proactive message.
    await send_main_menu(update.effective_chat.id, context)
    # Initialize user_data for storing strategies
    context.user_data['own_strategies'] = []
    return MAIN_MENU

async def default_choice(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """Handles the 'Default' choice and ends the conversation, then stops the bot."""
    await update.message.reply_text(
        "You chose 'Default'. Conversation ended. Shutting down bot...",
        reply_markup=ReplyKeyboardRemove(),
    )
    # Signal the application to stop polling. This will cause run_polling() to return.
    await context.application.stop()
    return ConversationHandler.END

async def robo_strategies_menu(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """Presents the Robo Strategies sub-menu."""
    reply_keyboard = [["Crypto"], ["Stocks"], ["Indices"]]
    await update.message.reply_text(
        "You chose 'Robo Strategies'. Please select a category:",
        reply_markup=ReplyKeyboardMarkup(
            reply_keyboard, one_time_keyboard=True, input_field_placeholder="Select a strategy"
        ),
    )
    return ROBO_STRATEGIES_MENU

async def handle_robo_strategy_choice(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """Handles the user's choice from Robo Strategies, ends the conversation, then stops the bot."""
    chosen_strategy = update.message.text
    await update.message.reply_text(
        f"You chose '{chosen_strategy}' from Robo Strategies. Conversation ended. Shutting down bot...",
        reply_markup=ReplyKeyboardRemove(),
    )
    # Signal the application to stop polling. This will cause run_polling() to return.
    await context.application.stop()
    return ConversationHandler.END

# --- Own Strategies Handlers ---

async def own_strategies_start(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """Initiates the 'Own Strategies' flow by asking for the ticker symbol."""
    await update.message.reply_text(
        "You chose 'Own Strategies'. Let's add your first strategy.\n"
        "Please specify the ticker symbol:",
        reply_markup=ReplyKeyboardRemove(), # Remove previous keyboard
    )
    return GET_TICKER

async def get_ticker_symbol(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """Stores the ticker symbol and asks about dividend reinvestment."""
    ticker_symbol = update.message.text.upper() # Convert to uppercase for consistency
    context.user_data['current_strategy_ticker'] = ticker_symbol

    reply_keyboard = [["Yes"], ["No"]]
    await update.message.reply_text(
        f"You entered '{ticker_symbol}'.\n"
        "Please specify whether to reinvest in dividends (Yes/No):",
        reply_markup=ReplyKeyboardMarkup(
            reply_keyboard, one_time_keyboard=True, input_field_placeholder="Yes or No?"
        ),
    )
    return GET_DIVIDEND_REINVEST

async def get_dividend_reinvest(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """Stores dividend reinvestment preference and asks if more strategies are needed."""
    reinvest_dividends = update.message.text.lower() == 'yes'
    ticker_symbol = context.user_data.get('current_strategy_ticker')

    # Store the completed strategy
    context.user_data['own_strategies'].append({
        'ticker': ticker_symbol,
        'reinvest_dividends': reinvest_dividends
    })
    logger.info("Added strategy: Ticker=%s, Reinvest Dividends=%s", ticker_symbol, reinvest_dividends)

    reply_keyboard = [["Yes", "No"]] # Offer 'Yes' and 'No' buttons
    await update.message.reply_text(
        "Strategy added successfully!\n"
        "Do you want to add another strategy? (Yes/No):",
        reply_markup=ReplyKeyboardMarkup(
            reply_keyboard, one_time_keyboard=True, input_field_placeholder="Yes or No?"
        ),
    )
    return ADD_ANOTHER_STRATEGY

async def add_another_strategy_prompt(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """Checks if the user wants to add another strategy or ends the conversation."""
    user_choice = update.message.text.lower()

    if user_choice in ('y', 'yes', ''): # Empty string for default 'y' if user just presses enter
        await update.message.reply_text(
            "Great! Please specify the ticker symbol for the next strategy:",
            reply_markup=ReplyKeyboardRemove(),
        )
        return GET_TICKER # Loop back to get ticker
    elif user_choice in ('n', 'no'):
        final_message = "Okay, all strategies added. Here's a summary of your own strategies:\n"
        if context.user_data['own_strategies']:
            for i, strategy in enumerate(context.user_data['own_strategies']):
                final_message += (
                    f"{i+1}. Ticker: {strategy['ticker']}, "
                    f"Reinvest Dividends: {'Yes' if strategy['reinvest_dividends'] else 'No'}\n"
                )
        else:
            final_message += "No strategies were added."

        await update.message.reply_text(
            final_message + "\nConversation ended. Shutting down bot...",
            reply_markup=ReplyKeyboardRemove(),
        )
        # Signal the application to stop polling. This will cause run_polling() to return.
        await context.application.stop()
        return ConversationHandler.END
    else:
        # Invalid input for 'add another strategy'
        reply_keyboard = [["Yes", "No"]]
        await update.message.reply_text(
            "Invalid input. Please answer 'Yes' or 'No' (or 'y'/'n').\n"
            "Do you want to add another strategy? (Yes/No):",
            reply_markup=ReplyKeyboardMarkup(
                reply_keyboard, one_time_keyboard=True, input_field_placeholder="Yes or No?"
            ),
        )
        return ADD_ANOTHER_STRATEGY # Stay in the same state

# --- Fallback Handlers ---

async def cancel(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """Cancels and ends the conversation, then stops the bot."""
    user = update.message.from_user
    logger.info("User %s canceled the conversation.", user.first_name)
    await update.message.reply_text(
        "Bye! I hope we can talk again some day. Shutting down bot...", reply_markup=ReplyKeyboardRemove()
    )
    # Clear any temporary user data for the conversation
    context.user_data.clear()
    # Signal the application to stop polling. This will cause run_polling() to return.
    await context.application.stop()
    return ConversationHandler.END

async def unknown_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    Handles messages that are not recognized commands or valid responses in the current state.
    This also signals the bot to shut down.
    """
    await update.message.reply_text(
        "Sorry, I didn't understand that command or response. "
        "Shutting down bot due to unhandled input. Please restart the script to interact again."
    )
    # Signal the application to stop polling. This will cause run_polling() to return.
    await context.application.stop()


async def post_init(application: Application) -> None:
    """
    Callback function that runs after the application has been initialized and started polling.
    This is the ideal place to send the proactive message.
    """
    try:
        await send_main_menu(TARGET_CHAT_ID, application)
        logger.info(f"Proactive message sent to chat ID: {TARGET_CHAT_ID}")
    except Exception as e:
        logger.error(f"Failed to send proactive message to {TARGET_CHAT_ID}: {e}")
        # If the proactive message fails, we should still signal to stop the application
        # so that main() can proceed to shutdown.
        await application.stop()

def main() -> None:
    """Run the bot."""
    # Create the Application and pass it your bot's token.
    application = Application.builder().token(YOUR_BOT_TOKEN).post_init(post_init).build()

    # Add conversation handler with the defined states
    conv_handler = ConversationHandler(
        # Entry points for starting the conversation (e.g., if user messages after bot has started)
        entry_points=[
            CommandHandler("start", start),
            MessageHandler(filters.TEXT & ~filters.COMMAND, start) # Any text that's not a command
        ],
        states={
            MAIN_MENU: [
                MessageHandler(filters.Regex("^Default$"), default_choice),
                MessageHandler(filters.Regex("^Robo Strategies$"), robo_strategies_menu),
                MessageHandler(filters.Regex("^Own Strategies$"), own_strategies_start),
            ],
            ROBO_STRATEGIES_MENU: [
                MessageHandler(filters.Regex("^(Crypto|Stocks|Indices)$"), handle_robo_strategy_choice),
            ],
            GET_TICKER: [
                MessageHandler(filters.TEXT & ~filters.COMMAND, get_ticker_symbol),
            ],
            GET_DIVIDEND_REINVEST: [
                MessageHandler(filters.Regex("^(Yes|No)$"), get_dividend_reinvest),
            ],
            ADD_ANOTHER_STRATEGY: [
                MessageHandler(filters.Regex("^(y|Y|n|N|Yes|No)$"), add_another_strategy_prompt),
            ],
        },
        fallbacks=[CommandHandler("cancel", cancel), MessageHandler(filters.TEXT | filters.COMMAND, unknown_command)],
    )

    application.add_handler(conv_handler)

    logger.info("Bot started. Awaiting user interaction or shutdown signal.")
    # Run the bot until a stop signal is received (e.g., from context.application.stop())
    # This call will block until application.stop() is called from a handler.
    application.run_polling(allowed_updates=Update.ALL_TYPES)
    logger.info("Bot has stopped polling.") # This log will appear once polling truly stops.

    # Perform final shutdown after run_polling returns.
    # This asyncio.run is crucial to ensure shutdown is awaited outside the main polling loop.
    asyncio.run(application.shutdown())
    logger.info("Application has fully shut down.")


if __name__ == "__main__":
    # If running in Jupyter/IPython, uncomment the following two lines:
    # import nest_asyncio
    # nest_asyncio.apply()

    main() # Call main directly, as run_polling handles the asyncio loop
