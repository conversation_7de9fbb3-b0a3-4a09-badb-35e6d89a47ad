{"cells": [{"cell_type": "code", "execution_count": 3, "id": "de20a0b8", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "eefa0555faf04703beb16731dd30021b", "version_major": 2, "version_minor": 0}, "text/plain": ["VBox(children=(HTML(value='<h2>Pipeline Registry Builder</h2><hr>'), But<PERSON>(button_style='success', descriptio…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import ipywidgets as widgets\n", "from IPython.display import display, clear_output\n", "import json\n", "import functools\n", "\n", "\"\"\"\n", "Provides an interactive UI within a Jupyter Notebook for building a\n", "pipeline registry dictionary.\n", "\n", "This module defines the `RegistryBuilder` class, which allows users to\n", "dynamically configure calculation steps and their parameters. The resulting\n", "registry can then be used to instantiate and run a data processing pipeline.\n", "The UI supports resolving string inputs to actual Python objects (like DataFrames\n", "or functions) present in the user's notebook environment if `globals()` is provided.\n", "\"\"\"\n", "\n", "# --- <PERSON><PERSON><PERSON> and <PERSON> (can be module-level or part of the class) ---\n", "CALCULATION_SCHEMAS = {\n", "    \"Base Case\": {\n", "        \"description\": \"Standard calculation with no special parameters (re-runs the main logic).\",\n", "        \"params\": {}\n", "    },\n", "    \"Change Rating Parameter\": {\n", "        \"description\": \"Modifies a specific rating parameter.\",\n", "        \"params\": {\n", "            \"change_rating\": {\n", "                \"type\": \"dropdown\",\n", "                \"options\": [\"use_other_rating_field\", \"use_primary_rating\", \"custom_rating_logic_var\"],\n", "                \"default\": \"use_other_rating_field\",\n", "                \"help\": \"Specify the rating field modification strategy or a variable name for custom logic.\"\n", "            }\n", "        }\n", "    },\n", "    \"Merge External Data\": {\n", "        \"description\": \"Merges the current data with an external dataset.\",\n", "        \"params\": {\n", "            \"merge_with_df_name\": { # This is a key example for variable_name resolution\n", "                \"type\": \"variable_name\",\n", "                \"help\": \"Name of the DataFrame (in your notebook) to merge with (e.g., 'my_df').\"\n", "            },\n", "            \"merge_on_columns\": {\n", "                \"type\": \"text\",\n", "                \"help\": \"Comma-separated list of columns to merge on (e.g., 'id,date').\"\n", "            },\n", "            \"how_to_merge\": {\n", "                \"type\": \"dropdown\",\n", "                \"options\": [\"left\", \"right\", \"inner\", \"outer\"],\n", "                \"default\": \"left\",\n", "                \"help\": \"Type of merge to perform.\"\n", "            }\n", "        }\n", "    },\n", "    \"Apply Custom Engine Logic\": {\n", "        \"description\": \"Applies custom transformation logic using named functions/variables.\",\n", "        \"params\": {\n", "            \"engine_settings\": {\n", "                \"type\": \"group\",\n", "                \"help\": \"Define engine parameters. Values should be names of functions/variables in your notebook.\",\n", "                \"sub_params\": {\n", "                    \"rating_transformer_func\": {\n", "                        \"type\": \"variable_name\",\n", "                        \"help\": \"Name of function to transform 'rating' (e.g., 'my_rating_shifter_func').\",\n", "                        \"optional\": True\n", "                    },\n", "                    \"default_value_setter_var\": {\n", "                        \"type\": \"variable_name\",\n", "                        \"help\": \"Name of variable holding default values (e.g., 'my_defaults_dict').\",\n", "                        \"optional\": True\n", "                    },\n", "                    \"pre_processing_script_name\": {\n", "                        \"type\": \"text\", # This remains text, not a variable_name\n", "                        \"help\": \"Name or path to a pre-processing script.\",\n", "                        \"optional\": True\n", "                    }\n", "                }\n", "            }\n", "        }\n", "    },\n", "    \"Filter Data\": {\n", "        \"description\": \"Applies a filter to the data.\",\n", "        \"params\": {\n", "            \"filter_column\": {\n", "                \"type\": \"text\",\n", "                \"help\": \"Column to filter on (e.g., 'status').\"\n", "            },\n", "            \"filter_value\": { # This value's interpretation is controlled by the flag below\n", "                \"type\": \"text\",\n", "                \"help\": \"Value to filter for (e.g., 'active', or if flag is set: 'my_filter_list_var').\"\n", "            },\n", "            \"is_variable_for_value\": { # This flag determines how 'filter_value' is treated by the Pipeline\n", "                \"type\": \"dropdown\",\n", "                \"options\": [(\"Direct Value\", False), (\"Notebook Variable\", True)],\n", "                \"default\": <PERSON><PERSON><PERSON>,\n", "                \"help\": \"Is the 'filter_value' a direct value or the name of a notebook variable?\"\n", "            }\n", "        }\n", "    }\n", "}\n", "\n", "# UI Styling and Layouts\n", "STYLE_DESCRIPTION_WIDTH = {'description_width': '180px'}\n", "STYLE_DESCRIPTION_WIDTH_LARGE = {'description_width': '150px'}\n", "LAYOUT_HALF_WIDTH = widgets.Layout(width='48%', margin='0 1% 5px 0')\n", "LAYOUT_FULL_WIDTH = widgets.Layout(width='98%', margin='0 1% 5px 0')\n", "LAYOUT_BUTTON = widgets.Layout(width='auto', margin='10px 5px 5px 0')\n", "SECTION_BORDER = '1px solid #ccc'\n", "SECTION_PADDING = '10px'\n", "\n", "\n", "class RegistryBuilder:\n", "    \"\"\"\n", "    Builds a pipeline registry dictionary through an interactive Jupyter widget UI.\n", "\n", "    This class provides a `build()` method that displays an interface allowing\n", "    users to define calculation steps, their types, and parameters. It supports\n", "    resolving parameter values that are names of variables in the user's\n", "    Jupyter notebook global scope into the actual Python objects.\n", "\n", "    Attributes:\n", "        calculation_schemas (dict): The schema defining available calculation\n", "            types and their parameters.\n", "        user_globals (dict): A dictionary representing the user's global\n", "            namespace, used for resolving variable names.\n", "        registry (dict): The pipeline registry dictionary being built.\n", "            This is updated in real-time as the user interacts with the UI.\n", "        calculation_step_configs (list): A list storing the configuration\n", "            and widgets for each added calculation step.\n", "        registry_output_display (widgets.Textarea): Widget to display the\n", "            JSON representation of the current registry.\n", "        calculation_steps_accordion (widgets.Accordion): Widget to organize\n", "            individual calculation step UIs.\n", "        output_area_for_dict_print (widgets.Output): Widget for printing\n", "            the Python dictionary representation of the registry.\n", "        full_ui (widgets.VBox): The main container widget for the entire UI.\n", "    \"\"\"\n", "    def __init__(self, calculation_schemas=None, user_globals=None):\n", "        \"\"\"Initializes the RegistryBuilder.\n", "\n", "        Args:\n", "            calculation_schemas (dict, optional): Custom schemas defining\n", "                calculation types and their parameters. If None, defaults to\n", "                `CALCULATION_SCHEMAS` defined in the module.\n", "            user_globals (dict, optional): The user's global namespace, typically\n", "                obtained by calling `globals()` in the notebook. This is\n", "                required for resolving 'variable_name' type parameters to\n", "                actual Python objects. Defaults to an empty dictionary if None.\n", "        \"\"\"\n", "        self.calculation_schemas = calculation_schemas or CALCULATION_SCHEMAS\n", "        self.user_globals = user_globals if user_globals is not None else {} # Store user's globals\n", "        self.calculation_step_configs = []\n", "        self.registry = {}\n", "\n", "        self.registry_output_display = widgets.Textarea(\n", "            value=self._format_registry_for_display(self.registry), # Use helper for initial display\n", "            description='Generated Registry:',\n", "            disabled=True,\n", "            layout=widgets.Layout(width='98%', height='200px', margin='10px 1%'),\n", "            style=STYLE_DESCRIPTION_WIDTH_LARGE\n", "        )\n", "        self.calculation_steps_accordion = widgets.Accordion(children=[], selected_index=None)\n", "        self.calculation_steps_accordion.layout.width = '100%'\n", "        self.output_area_for_dict_print = widgets.Output()\n", "        self.full_ui = None\n", "\n", "    def _simple_custom_item_repr(self, item_value):\n", "        \"\"\"Creates a simple string representation for non-JSON serializable objects.\n", "\n", "        This is used to make the live registry display in the UI more informative\n", "        when it contains objects like DataFrames or functions.\n", "\n", "        Args:\n", "            item_value (Any): The item to represent.\n", "\n", "        Returns:\n", "            Any: A JSON-serializable representation of the item. For non-basic\n", "                 types, it returns a string like \"<TypeName object (in-memory)>\".\n", "        \"\"\"\n", "        if isinstance(item_value, dict):\n", "            return {k: self._simple_custom_item_repr(v) for k, v in item_value.items()}\n", "        elif isinstance(item_value, list):\n", "            return [self._simple_custom_item_repr(i) for i in item_value]\n", "        # Check for common non-serializable types or any object that isn't basic\n", "        elif not isinstance(item_value, (str, int, float, bool, type(None))):\n", "            return f\"<{type(item_value).__name__} object (in-memory)>\"\n", "        else:\n", "            return item_value\n", "\n", "    def _format_registry_for_display(self, registry_dict):\n", "        \"\"\"Formats the registry dictionary for display in a Textarea.\n", "\n", "        Handles in-memory objects by converting them to a string representation\n", "        using `_simple_custom_item_repr` before attempting JSON serialization.\n", "\n", "        Args:\n", "            registry_dict (dict): The registry dictionary to format.\n", "\n", "        Returns:\n", "            str: A JSON string representation of the registry, suitable for display.\n", "        \"\"\"\n", "        displayable_dict = self._simple_custom_item_repr(registry_dict)\n", "        try:\n", "            return json.dumps(displayable_dict, indent=4)\n", "        except TypeError: # Fallback if _simple_custom_item_repr missed something\n", "            return json.dumps(displayable_dict, indent=4, default=str)\n", "\n", "\n", "    def _create_param_widget(self, param_name, param_config, calculation_id):\n", "        \"\"\"Creates an input widget for a single parameter based on its schema.\n", "\n", "        Args:\n", "            param_name (str): The name of the parameter.\n", "            param_config (dict): The schema configuration for this parameter.\n", "            calculation_id (int): The ID of the calculation step this parameter\n", "                belongs to, used for linking callbacks.\n", "\n", "        Returns:\n", "            widgets.VBox or None: A VBox containing the input widget and its\n", "                help text, or None if the widget type is not supported.\n", "        \"\"\"\n", "        label = param_name.replace('_', ' ').title()\n", "        if param_config.get(\"optional\", False):\n", "            label += \" (Optional)\"\n", "        help_text_widget = widgets.HTML(\n", "            f\"<p style='font-size:0.9em; color:grey; margin:0 0 5px 0;'><i>{param_config.get('help', '')}</i></p>\",\n", "            layout=LAYOUT_FULL_WIDTH\n", "        )\n", "        widget_instance = None\n", "        if param_config['type'] == 'text' or param_config['type'] == 'variable_name':\n", "            widget_instance = widgets.Text(description=label, style=STYLE_DESCRIPTION_WIDTH, layout=LAYOUT_HALF_WIDTH)\n", "        elif param_config['type'] == 'dropdown':\n", "            options = param_config['options']\n", "            if not options or not isinstance(options[0], tuple): # Ensure (label, value) format\n", "                 options = [(str(opt), opt) for opt in options]\n", "            widget_instance = widgets.Dropdown(\n", "                options=options, value=param_config.get('default'),\n", "                description=label, style=STYLE_DESCRIPTION_WIDTH, layout=LAYOUT_HALF_WIDTH\n", "            )\n", "        if widget_instance:\n", "            # Use functools.partial to pass additional arguments to the callback\n", "            callback = functools.partial(self._update_registry_on_param_change, calculation_id=calculation_id, param_name_key=param_name)\n", "            widget_instance.observe(callback, names='value')\n", "            return widgets.VBox([widget_instance, help_text_widget], layout=widgets.Layout(margin='0 0 10px 0'))\n", "        return None\n", "\n", "    def _create_group_param_widgets(self, group_name, group_config, calculation_id):\n", "        \"\"\"Creates a VBox containing widgets for a group of sub-parameters.\n", "\n", "        Args:\n", "            group_name (str): The name of the parameter group.\n", "            group_config (dict): The schema configuration for this group,\n", "                including its 'sub_params'.\n", "            calculation_id (int): The ID of the calculation step.\n", "\n", "        Returns:\n", "            tuple: A tuple containing:\n", "                - widgets.VBox: The VBox holding all widgets for the group.\n", "                - dict: A dictionary mapping sub-parameter names to their\n", "                        actual input widgets (not the VBox wrappers).\n", "        \"\"\"\n", "        group_label = widgets.HTML(\n", "            f\"<h4>{group_name.replace('_', ' ').title()} Parameters:</h4>\"\n", "            f\"<p style='font-size:0.9em; color:grey; margin:0 0 10px 0;'><i>{group_config.get('help', '')}</i></p>\"\n", "        )\n", "        sub_param_widgets_list = [group_label]\n", "        sub_param_actual_widgets = {} # To store direct references to input widgets\n", "        for sub_param_name, sub_param_config in group_config.get('sub_params', {}).items():\n", "            sub_widget_vbox = self._create_param_widget(sub_param_name, sub_param_config, calculation_id)\n", "            if sub_widget_vbox:\n", "                sub_param_widgets_list.append(sub_widget_vbox)\n", "                # Store the actual input widget (child of the VBox)\n", "                sub_param_actual_widgets[sub_param_name] = sub_widget_vbox.children[0]\n", "        group_box = widgets.VBox(\n", "            sub_param_widgets_list,\n", "            layout=widgets.Layout(border=SECTION_BORDER, padding=SECTION_PADDING, margin='10px 0')\n", "        )\n", "        return group_box, sub_param_actual_widgets\n", "\n", "    def _populate_params_for_type(self, selected_type, params_box, calculation_id):\n", "        \"\"\"Populates parameter input widgets based on the selected calculation type.\n", "\n", "        This method clears existing parameter widgets in `params_box` and adds\n", "        new ones according to the schema of the `selected_type`.\n", "\n", "        Args:\n", "            selected_type (str): The chosen calculation type (e.g., \"Merge External Data\").\n", "            params_box (widgets.VBox): The VBox container where parameter\n", "                widgets will be placed.\n", "            calculation_id (int): The ID of the current calculation step.\n", "        \"\"\"\n", "        params_box.children = [] # Clear previous params\n", "        step_config = next(c for c in self.calculation_step_configs if c['id'] == calculation_id)\n", "        step_config['param_widgets'] = {} # Reset param widgets for this step\n", "\n", "        schema = self.calculation_schemas.get(selected_type, {})\n", "        param_definitions = schema.get('params', {})\n", "\n", "        if not param_definitions:\n", "            params_box.children = [widgets.Label(\"No parameters for this type.\")]\n", "            self._update_registry() # Update registry even if no params\n", "            return\n", "\n", "        new_widgets = []\n", "        for param_name, param_config in param_definitions.items():\n", "            if param_config['type'] == 'group':\n", "                group_box, sub_param_widgets_dict = self._create_group_param_widgets(param_name, param_config, calculation_id)\n", "                new_widgets.append(group_box)\n", "                step_config['param_widgets'][param_name] = sub_param_widgets_dict # Store dict of sub-widgets\n", "            else:\n", "                widget_vbox = self._create_param_widget(param_name, param_config, calculation_id)\n", "                if widget_vbox:\n", "                    new_widgets.append(widget_vbox)\n", "                    # Store the actual input widget (child of the VBox)\n", "                    step_config['param_widgets'][param_name] = widget_vbox.children[0]\n", "        \n", "        params_box.children = new_widgets\n", "        self._update_registry() # Update registry after populating params\n", "\n", "    def _update_registry_on_param_change(self, change, calculation_id, param_name_key):\n", "        \"\"\"\n", "        Callback for when a parameter widget's value changes. Triggers registry update.\n", "\n", "        Args:\n", "            change (dict): The change event dictionary from the widget.\n", "            calculation_id (int): The ID of the calculation step whose parameter changed.\n", "            param_name_key (str): The name of the parameter that changed.\n", "        \"\"\"\n", "        self._update_registry()\n", "\n", "    def _update_registry_on_step_name_or_type_change(self, change, calculation_id):\n", "        \"\"\"\n", "        Callback for when a step's name or calculation type changes.\n", "\n", "        If the type changes, it repopulates the parameter widgets. Otherwise,\n", "        it just updates the registry.\n", "\n", "        Args:\n", "            change (dict): The change event dictionary from the widget.\n", "            calculation_id (int): The ID of the calculation step that changed.\n", "        \"\"\"\n", "        if change['name'] == 'value' and change['type'] == 'change': # Ensure it's a value change\n", "            step_config = next(c for c in self.calculation_step_configs if c['id'] == calculation_id)\n", "            if change['owner'] == step_config['type_widget']: # Calculation type changed\n", "                self._populate_params_for_type(change['new'], step_config['params_box'], calculation_id)\n", "            else: # Step name changed or initial population\n", "                self._update_registry()\n", "\n", "    def _update_registry(self):\n", "        \"\"\"\n", "        Rebuilds the internal `self.registry` dictionary based on current UI state.\n", "\n", "        Iterates through all configured calculation steps, retrieves their parameter\n", "        values, resolves 'variable_name' types against `self.user_globals` if\n", "        provided, and constructs the nested dictionary. Updates\n", "        `self.registry_output_display` with the new JSON representation.\n", "        \"\"\"\n", "        new_registry = {}\n", "        for config in self.calculation_step_configs:\n", "            step_name = config['name_widget'].value.strip()\n", "            if not step_name: # Default name if user leaves it blank\n", "                step_name = f\"unnamed_step_{config['id']}\"\n", "\n", "            params_data = {}\n", "            # Get the schema for the current calculation type to access param definitions\n", "            calc_type_schema = self.calculation_schemas.get(config['type_widget'].value, {})\n", "            calc_type_params_schema = calc_type_schema.get('params', {})\n", "\n", "            for param_key, widget_or_dict in config['param_widgets'].items():\n", "                param_schema = calc_type_params_schema.get(param_key, {}) # Schema for current param\n", "\n", "                if isinstance(widget_or_dict, dict): # It's a group of sub-parameters\n", "                    group_data = {}\n", "                    sub_param_schema_group = param_schema.get('sub_params', {})\n", "                    for sub_param_key, sub_widget_instance in widget_or_dict.items():\n", "                        raw_sub_value = sub_widget_instance.value\n", "                        sub_param_item_schema = sub_param_schema_group.get(sub_param_key, {})\n", "                        \n", "                        is_optional_sub = sub_param_item_schema.get(\"optional\", False)\n", "                        if is_optional_sub and (raw_sub_value is None or str(raw_sub_value).strip() == \"\"):\n", "                            continue # Skip empty optional fields\n", "\n", "                        sub_final_value = raw_sub_value # Default to raw value\n", "                        # Attempt to resolve if 'variable_name' type and user_globals available\n", "                        if sub_param_item_schema.get('type') == 'variable_name' and self.user_globals:\n", "                            if isinstance(raw_sub_value, str) and raw_sub_value.strip(): # Ensure it's a non-empty string\n", "                                var_name_str = raw_sub_value.strip()\n", "                                resolved_object = self.user_globals.get(var_name_str)\n", "                                # If found in globals, use the object, else keep the string name\n", "                                sub_final_value = resolved_object if resolved_object is not None else var_name_str\n", "                        elif isinstance(sub_widget_instance, widgets.Dropdown) and isinstance(raw_sub_value, bool):\n", "                            sub_final_value = raw_sub_value # Store boolean directly\n", "                        else: # Default stringification for other sub-params\n", "                             # Avoid re-stringifying if it was already resolved to an object\n", "                             if not (sub_param_item_schema.get('type') == 'variable_name' and \\\n", "                                isinstance(raw_sub_value, str) and raw_sub_value.strip() and \\\n", "                                sub_final_value is self.user_globals.get(raw_sub_value.strip(), object())): # Check if it was resolved\n", "                                sub_final_value = str(raw_sub_value) if raw_sub_value is not None else None\n", "                        group_data[sub_param_key] = sub_final_value\n", "                    if group_data: # Only add group to params_data if it has content\n", "                        params_data[param_key] = group_data\n", "\n", "                elif hasattr(widget_or_dict, 'value'): # It's a single widget\n", "                    raw_value = widget_or_dict.value\n", "                    \n", "                    is_optional = param_schema.get(\"optional\", False)\n", "                    if is_optional and (raw_value is None or str(raw_value).strip() == \"\"):\n", "                         continue # Skip empty optional fields\n", "\n", "                    current_param_final_value = raw_value # Default to raw value\n", "\n", "                    # Attempt to resolve if 'variable_name' type and user_globals available\n", "                    if param_schema.get('type') == 'variable_name' and self.user_globals:\n", "                        if isinstance(raw_value, str) and raw_value.strip(): # Ensure it's a non-empty string\n", "                            var_name_str = raw_value.strip()\n", "                            resolved_object = self.user_globals.get(var_name_str)\n", "                            # If found in globals, use the object, else keep the string name\n", "                            current_param_final_value = resolved_object if resolved_object is not None else var_name_str\n", "                    elif isinstance(widget_or_dict, widgets.Dropdown) and isinstance(raw_value, bool):\n", "                        current_param_final_value = raw_value # Store boolean directly\n", "                    else:\n", "                        # Stringify if not a bool and not already resolved to an object\n", "                        is_resolved_object = False\n", "                        # Check if it's a variable_name type that was successfully resolved\n", "                        if param_schema.get('type') == 'variable_name' and isinstance(raw_value, str) and raw_value.strip() and self.user_globals:\n", "                             # Compare with a unique object to see if get() returned the default (object()) or a real value\n", "                             if current_param_final_value is self.user_globals.get(raw_value.strip(), object()):\n", "                                is_resolved_object = True\n", "                        \n", "                        if not isinstance(current_param_final_value, bool) and not is_resolved_object:\n", "                            current_param_final_value = str(raw_value) if raw_value is not None else None\n", "                    \n", "                    params_data[param_key] = current_param_final_value\n", "            new_registry[step_name] = params_data\n", "        \n", "        self.registry = new_registry # Update the instance attribute\n", "        self.registry_output_display.value = self._format_registry_for_display(self.registry)\n", "\n", "    def _on_remove_step_clicked(self, b, calculation_id):\n", "        \"\"\"\n", "        Callback for the 'Remove This Step' button.\n", "\n", "        Removes the specified calculation step's UI from the accordion and its\n", "        configuration from `self.calculation_step_configs`. Updates the registry.\n", "\n", "        Args:\n", "            b (widgets.<PERSON><PERSON>): The button instance that was clicked.\n", "            calculation_id (int): The ID of the calculation step to remove.\n", "        \"\"\"\n", "        config_to_remove = next((c for c in self.calculation_step_configs if c['id'] == calculation_id), None)\n", "        idx_to_remove = -1 # Index in accordion.children\n", "\n", "        # Find the VBox and its index in the accordion's children\n", "        for i, child_vbox in enumerate(self.calculation_steps_accordion.children):\n", "            if hasattr(child_vbox, 'calculation_id_tag') and child_vbox.calculation_id_tag == calculation_id:\n", "                idx_to_remove = i\n", "                break\n", "        \n", "        if idx_to_remove != -1 and config_to_remove:\n", "            self.calculation_step_configs.remove(config_to_remove)\n", "            \n", "            new_children = list(self.calculation_steps_accordion.children)\n", "            new_children.pop(idx_to_remove) # Remove by index\n", "            self.calculation_steps_accordion.children = tuple(new_children)\n", "            \n", "            # Re-title remaining accordion children correctly\n", "            for i, child in enumerate(self.calculation_steps_accordion.children):\n", "                # Find the corresponding config for the remaining child\n", "                current_step_config = next((c for c in self.calculation_step_configs if c['id'] == child.calculation_id_tag), None)\n", "                if current_step_config:\n", "                    title = current_step_config['name_widget'].value.strip() or f\"Step (ID: {current_step_config['id']})\"\n", "                    self.calculation_steps_accordion.set_title(i, title)\n", "\n", "            # Adjust selected_index if necessary\n", "            if not self.calculation_steps_accordion.children: # If all removed\n", "                self.calculation_steps_accordion.selected_index = None\n", "            elif self.calculation_steps_accordion.selected_index is not None and \\\n", "                 self.calculation_steps_accordion.selected_index >= len(self.calculation_steps_accordion.children):\n", "                 self.calculation_steps_accordion.selected_index = len(self.calculation_steps_accordion.children) - 1\n", "            \n", "            self._update_registry()\n", "\n", "    def _update_accordion_title(self, change, accordion_idx, step_name_widget):\n", "        \"\"\"\n", "        Callback to update an accordion pane's title when its step name changes.\n", "\n", "        Args:\n", "            change (dict): The change event from the step_name_widget.\n", "            accordion_idx (int): The index of the accordion pane to update.\n", "            step_name_widget (widgets.Text): The text widget for the step name.\n", "        \"\"\"\n", "        title_to_set = step_name_widget.value.strip() # Use the widget passed to the callback\n", "        if not title_to_set:\n", "            # Find the calculation_id associated with this step_name_widget to create a default title\n", "            assoc_config = next((c for c in self.calculation_step_configs if c['name_widget'] == step_name_widget), None)\n", "            calc_id_for_title = assoc_config['id'] if assoc_config else accordion_idx +1 # Fallback ID\n", "            title_to_set = f\"Step (ID: {calc_id_for_title})\"\n", "\n", "        if accordion_idx < len(self.calculation_steps_accordion.children): # Check index validity\n", "            self.calculation_steps_accordion.set_title(accordion_idx, title_to_set)\n", "\n", "    def _add_calculation_step_ui(self, b=None):\n", "        \"\"\"\n", "        Adds UI elements for a new calculation step to the accordion.\n", "\n", "        This involves creating widgets for step name, calculation type, parameters\n", "        (initially empty, populated by `_populate_params_for_type`), and a\n", "        remove button.\n", "\n", "        Args:\n", "            b (widgets.<PERSON><PERSON>, optional): The button instance that triggered\n", "                this action (if any). Defaults to None.\n", "        \"\"\"\n", "        # Use a more robust ID, e.g., based on current max ID + 1\n", "        current_max_id = -1\n", "        if self.calculation_step_configs:\n", "            current_max_id = max(c['id'] for c in self.calculation_step_configs)\n", "        calculation_id = current_max_id + 1\n", "\n", "        step_name_widget = widgets.Text(\n", "            description=\"Step Name:\", placeholder=\"e.g., base_case\",\n", "            style=STYLE_DESCRIPTION_WIDTH, layout=LAYOUT_FULL_WIDTH\n", "        )\n", "        calculation_type_widget = widgets.Dropdown(\n", "            options=list(self.calculation_schemas.keys()), description=\"Calculation Type:\",\n", "            style=STYLE_DESCRIPTION_WIDTH, layout=LAYOUT_FULL_WIDTH\n", "        )\n", "        params_widgets_box = widgets.VBox([], layout=widgets.Layout(margin='10px 0 0 20px')) # Indent params\n", "\n", "        step_config_data = {\n", "            'id': calculation_id, 'name_widget': step_name_widget,\n", "            'type_widget': calculation_type_widget, 'params_box': params_widgets_box,\n", "            'param_widgets': {} # To be filled by _populate_params_for_type\n", "        }\n", "        self.calculation_step_configs.append(step_config_data)\n", "\n", "        # Observers for name and type changes\n", "        name_cb = functools.partial(self._update_registry_on_step_name_or_type_change, calculation_id=calculation_id)\n", "        step_name_widget.observe(name_cb, names='value')\n", "        \n", "        type_cb = functools.partial(self._update_registry_on_step_name_or_type_change, calculation_id=calculation_id)\n", "        calculation_type_widget.observe(type_cb, names='value')\n", "\n", "        remove_step_button = widgets.Button(\n", "            description=\"Remove This Step\", button_style='danger', icon='trash', layout=LAYOUT_BUTTON\n", "        )\n", "        # Pass calculation_id to the remove callback\n", "        remove_cb = functools.partial(self._on_remove_step_clicked, calculation_id=calculation_id)\n", "        remove_step_button.on_click(remove_cb)\n", "        \n", "        step_header_box = widgets.VBox([step_name_widget, calculation_type_widget])\n", "        step_ui_vbox = widgets.VBox(\n", "            [step_header_box, params_widgets_box, remove_step_button],\n", "            layout=widgets.Layout(border=SECTION_BORDER, padding=SECTION_PADDING, margin='5px 0')\n", "        )\n", "        step_ui_vbox.calculation_id_tag = calculation_id # Tag for removal logic\n", "\n", "        current_children = list(self.calculation_steps_accordion.children)\n", "        current_children.append(step_ui_vbox)\n", "        self.calculation_steps_accordion.children = tuple(current_children)\n", "        \n", "        accordion_idx = len(self.calculation_steps_accordion.children) - 1\n", "        \n", "        # Set initial title for the accordion pane\n", "        initial_title = f\"Step (ID: {calculation_id})\" # Default title before name is set\n", "        self.calculation_steps_accordion.set_title(accordion_idx, initial_title)\n", "        \n", "        # Observer for step name to update accordion title dynamically\n", "        # Pass the step_name_widget itself to the callback to get its current value\n", "        title_update_cb = functools.partial(self._update_accordion_title, accordion_idx=accordion_idx, step_name_widget=step_name_widget)\n", "        step_name_widget.observe(title_update_cb, names='value')\n", "\n", "        # Trigger initial population of params for the default type\n", "        self._populate_params_for_type(calculation_type_widget.value, params_widgets_box, calculation_id)\n", "        \n", "        self.calculation_steps_accordion.selected_index = accordion_idx # Select the new pane\n", "        self._update_registry() # Ensure registry is updated after adding a new step\n", "\n", "    def _get_registry_dict_button_clicked(self, b):\n", "        \"\"\"\n", "        Callback for the 'Print Registry Dict' button.\n", "\n", "        Prints the current `self.registry` dictionary (which may contain\n", "        in-memory objects) to `self.output_area_for_dict_print` using `pprint`.\n", "\n", "        Args:\n", "            b (widgets.<PERSON><PERSON>): The button instance that was clicked.\n", "        \"\"\"\n", "        with self.output_area_for_dict_print:\n", "            clear_output(wait=True)\n", "            print(\"Current Registry Python Dictionary (also available as builder.registry):\")\n", "            import pprint\n", "            pprint.pprint(self.registry)\n", "\n", "    def build(self):\n", "        \"\"\"\n", "        Assembles and displays the main UI for the RegistryBuilder.\n", "\n", "        If the UI has already been built, it simply re-displays it. Otherwise,\n", "        it constructs the VBox containing all UI elements and uses `display()`\n", "        to render it in the Jupyter Notebook. Adds one default calculation\n", "        step if none exist.\n", "        \"\"\"\n", "        if self.full_ui: # If called again, just re-display the existing UI\n", "            display(self.full_ui)\n", "            return\n", "\n", "        add_button = widgets.Button(\n", "            description=\"Add New Calculation Step\", icon='plus',\n", "            button_style='success', layout=LAYOUT_BUTTON\n", "        )\n", "        add_button.on_click(self._add_calculation_step_ui)\n", "\n", "        get_registry_button = widgets.Button(\n", "            description=\"Print Registry Dict\", icon='print', # Changed icon\n", "            button_style='info', layout=LAYOUT_BUTTON\n", "        )\n", "        get_registry_button.on_click(self._get_registry_dict_button_clicked)\n", "\n", "        ui_title = widgets.HTML(\"<h2>Pipeline Registry Builder</h2><hr>\")\n", "\n", "        self.full_ui = widgets.VBox([\n", "            ui_title,\n", "            add_button,\n", "            self.calculation_steps_accordion,\n", "            self.registry_output_display,\n", "            get_registry_button,\n", "            self.output_area_for_dict_print\n", "        ], layout=widgets.Layout(width='70%', margin='auto', padding=SECTION_PADDING,\n", "                                 border='1px solid #A0A0A0', background_color='#f9f9f9'))\n", "        \n", "        if not self.calculation_step_configs: # Add one step by default if empty on first build\n", "             self._add_calculation_step_ui()\n", "\n", "        display(self.full_ui)\n", "\n", "\n", "import pandas as pd # Assuming pandas is used for DataFrames\n", "#\n", "# # In a Jupyter Notebook cell:\n", "# # Create a dummy DataFrame and a function for testing\n", "df_right = pd.DataFrame({'A': [1, 2], 'B': [3, 4]})\n", "def my_rating_shifter_func(rating_series):\n", "    return rating_series * 1.1\n", "#\n", "\n", "def rating_modifier(r):\n", "    return r + 1\n", "\n", "builder = RegistryBuilder(user_globals=globals()) # Pass the notebook's globals\n", "builder.build()\n", "\n"]}, {"cell_type": "code", "execution_count": 4, "id": "921e7a31", "metadata": {}, "outputs": [], "source": ["import ipywidgets as widgets\n", "from IPython.display import display, clear_output\n", "import json\n", "import functools\n", "import uuid # For unique block IDs within a step\n", "\n", "# --- <PERSON><PERSON><PERSON> and <PERSON> (CALCULATION_SCHEMAS should be defined as in your last working version) ---\n", "CALCULATION_SCHEMAS = {\n", "    \"Base Case\": {\n", "        \"description\": \"Standard calculation with no special parameters (re-runs the main logic).\",\n", "        \"params\": {}\n", "    },\n", "    \"Change Rating Parameter\": {\n", "        \"description\": \"Modifies a specific rating parameter.\",\n", "        \"params\": {\n", "            # Assuming 'exchange_rating' in your example maps to this 'change_rating' key\n", "            \"change_rating\": {\n", "                \"type\": \"dropdown\",\n", "                \"options\": [\"use_other_rating_field\", \"use_primary_rating\", \"custom_rating_logic_var\", \"new_rating_columns\"],\n", "                \"default\": \"use_other_rating_field\",\n", "                \"help\": \"Specify the rating field modification strategy or a variable name for custom logic.\"\n", "            }\n", "        }\n", "    },\n", "    \"Merge External Data\": { # Example, if 'initial_merge' refers to this\n", "        \"description\": \"Merges the current data with an external dataset.\",\n", "        \"params\": {\n", "            \"merge_with_df_name\": {\n", "                \"type\": \"variable_name\",\n", "                \"help\": \"Name of the DataFrame (in your notebook) to merge with (e.g., 'my_df').\"\n", "            },\n", "            \"merge_on_columns\": {\n", "                \"type\": \"text\",\n", "                \"help\": \"Comma-separated list of columns to merge on (e.g., 'id,date').\"\n", "            },\n", "            \"how_to_merge\": {\n", "                \"type\": \"dropdown\",\n", "                \"options\": [\"left\", \"right\", \"inner\", \"outer\"],\n", "                \"default\": \"left\",\n", "                \"help\": \"Type of merge to perform.\"\n", "            }\n", "        }\n", "    },\n", "     \"Step With Prior Merge\": { # For the prior_merge example\n", "        \"description\": \"A step that includes a configurable prior merge operation.\",\n", "        \"params\": {\n", "            \"prior_merge\": {\n", "                \"type\": \"group\",\n", "                \"help\": \"Define prior merge: data (DataFrame name), on (comma-sep columns), how (merge type).\",\n", "                \"sub_params\": {\n", "                    \"data\": {\n", "                        \"type\": \"variable_name\",\n", "                        \"help\": \"Name of the DataFrame variable for prior data (e.g., 'df_right_rating').\"\n", "                    },\n", "                    \"on\": {\n", "                        \"type\": \"text\",\n", "                        \"help\": \"Comma-separated column names to merge on (e.g., 'id1,id2').\"\n", "                    },\n", "                    \"how\": {\n", "                        \"type\": \"dropdown\",\n", "                        \"options\": [\"left\", \"right\", \"inner\", \"outer\", \"cross\"],\n", "                        \"default\": \"inner\", # Matching example\n", "                        \"help\": \"Type of merge to perform.\"\n", "                    }\n", "                }\n", "            }\n", "        }\n", "    }\n", "    # ... other schemas as needed\n", "}\n", "\n", "STYLE_DESCRIPTION_WIDTH = {'description_width': '180px'}\n", "STYLE_DESCRIPTION_WIDTH_LARGE = {'description_width': '150px'}\n", "STYLE_DESCRIPTION_WIDTH_MEDIUM = {'description_width': '130px'} # For block type\n", "LAYOUT_HALF_WIDTH = widgets.Layout(width='48%', margin='0 1% 5px 0')\n", "LAYOUT_FULL_WIDTH = widgets.Layout(width='98%', margin='0 1% 5px 0')\n", "LAYOUT_BUTTON = widgets.Layout(width='auto', margin='10px 5px 5px 0')\n", "SECTION_BORDER = '1px solid #ccc'\n", "BLOCK_BORDER = '1px dashed #7570b3' # Border for logic blocks\n", "SECTION_PADDING = '10px'\n", "BLOCK_PADDING = '8px'\n", "BLOCK_MARGIN = '10px 0 5px 0'\n", "\n", "\n", "class RegistryBuilder:\n", "    def __init__(self, calculation_schemas=None, user_globals=None):\n", "        self.calculation_schemas = calculation_schemas or CALCULATION_SCHEMAS\n", "        self.user_globals = user_globals if user_globals is not None else {}\n", "        self.calculation_step_configs = [] # Main UI steps (scenarios)\n", "        self.registry = {}\n", "        self.registry_output_display = widgets.Textarea(\n", "            value=self._format_registry_for_display(self.registry),\n", "            description='Generated Registry:',\n", "            disabled=True,\n", "            layout=widgets.Layout(width='98%', height='200px', margin='10px 1%'),\n", "            style=STYLE_DESCRIPTION_WIDTH_LARGE\n", "        )\n", "        self.calculation_steps_accordion = widgets.Accordion(children=[], selected_index=None)\n", "        self.calculation_steps_accordion.layout.width = '100%'\n", "        self.output_area_for_dict_print = widgets.Output()\n", "        self.full_ui = None\n", "        self._next_step_id_counter = 0\n", "\n", "    def _get_next_step_id(self):\n", "        self._next_step_id_counter += 1\n", "        return self._next_step_id_counter - 1\n", "\n", "    def _simple_custom_item_repr(self, item_value): # Same as before\n", "        if isinstance(item_value, dict):\n", "            return {k: self._simple_custom_item_repr(v) for k, v in item_value.items()}\n", "        elif isinstance(item_value, list):\n", "            return [self._simple_custom_item_repr(i) for i in item_value]\n", "        elif not isinstance(item_value, (str, int, float, bool, type(None))):\n", "            return f\"<{type(item_value).__name__} object (in-memory)>\"\n", "        else:\n", "            return item_value\n", "\n", "    def _format_registry_for_display(self, registry_dict): # Same as before\n", "        displayable_dict = self._simple_custom_item_repr(registry_dict)\n", "        try:\n", "            return json.dumps(displayable_dict, indent=4)\n", "        except TypeError: # Fallback\n", "            return json.dumps(displayable_dict, indent=4, default=str)\n", "\n", "    def _create_param_widget(self, param_name, param_config, step_id, block_id, param_group_key=None):\n", "        label = param_name.replace('_', ' ').title()\n", "        if param_config.get(\"optional\", False): label += \" (Optional)\"\n", "        help_text_widget = widgets.HTML(\n", "            f\"<p style='font-size:0.9em; color:grey; margin:0 0 5px 0;'><i>{param_config.get('help', '')}</i></p>\",\n", "            layout=LAYOUT_FULL_WIDTH\n", "        )\n", "        widget_instance = None\n", "        if param_config['type'] == 'text' or param_config['type'] == 'variable_name':\n", "            widget_instance = widgets.Text(description=label, style=STYLE_DESCRIPTION_WIDTH, layout=LAYOUT_HALF_WIDTH)\n", "        elif param_config['type'] == 'dropdown':\n", "            options = param_config['options']\n", "            if not options or not isinstance(options[0], tuple):\n", "                 options = [(str(opt), opt) for opt in options]\n", "            widget_instance = widgets.Dropdown(\n", "                options=options, value=param_config.get('default'),\n", "                description=label, style=STYLE_DESCRIPTION_WIDTH, layout=LAYOUT_HALF_WIDTH\n", "            )\n", "        if widget_instance:\n", "            callback = functools.partial(self._trigger_registry_update, entity_type='param', step_id=step_id, block_id=block_id, param_name_key=param_name, param_group_key=param_group_key)\n", "            widget_instance.observe(callback, names='value')\n", "            return widgets.VBox([widget_instance, help_text_widget], layout=widgets.Layout(margin='0 0 10px 0'))\n", "        return None\n", "\n", "    def _create_group_param_widgets(self, group_name, group_config, step_id, block_id):\n", "        group_label = widgets.HTML(\n", "            f\"<h4>{group_name.replace('_', ' ').title()} Parameters:</h4>\"\n", "            f\"<p style='font-size:0.9em; color:grey; margin:0 0 10px 0;'><i>{group_config.get('help', '')}</i></p>\"\n", "        )\n", "        sub_param_widgets_list = [group_label]\n", "        sub_param_actual_widgets = {}\n", "        for sub_param_name, sub_param_config in group_config.get('sub_params', {}).items():\n", "            sub_widget_vbox = self._create_param_widget(sub_param_name, sub_param_config, step_id, block_id, param_group_key=group_name)\n", "            if sub_widget_vbox:\n", "                sub_param_widgets_list.append(sub_widget_vbox)\n", "                sub_param_actual_widgets[sub_param_name] = sub_widget_vbox.children[0]\n", "        group_box = widgets.VBox(\n", "            sub_param_widgets_list,\n", "            layout=widgets.Layout(border=SECTION_BORDER, padding=SECTION_PADDING, margin='10px 0')\n", "        )\n", "        return group_box, sub_param_actual_widgets\n", "\n", "    def _populate_params_for_block(self, selected_type, params_box_for_block, step_id, block_id):\n", "        params_box_for_block.children = []\n", "        step_config = next(c for c in self.calculation_step_configs if c['id'] == step_id)\n", "        block_config = next(b for b in step_config['logic_blocks'] if b['id'] == block_id)\n", "        block_config['param_widgets'] = {}\n", "\n", "        schema = self.calculation_schemas.get(selected_type, {})\n", "        param_definitions = schema.get('params', {})\n", "\n", "        if not param_definitions:\n", "            params_box_for_block.children = [widgets.Label(\"No parameters for this type.\")]\n", "            self._update_registry()\n", "            return\n", "\n", "        new_widgets = []\n", "        for param_name, param_config in param_definitions.items():\n", "            if param_config['type'] == 'group':\n", "                group_box, sub_param_widgets_dict = self._create_group_param_widgets(param_name, param_config, step_id, block_id)\n", "                new_widgets.append(group_box)\n", "                block_config['param_widgets'][param_name] = sub_param_widgets_dict\n", "            else:\n", "                widget_vbox = self._create_param_widget(param_name, param_config, step_id, block_id)\n", "                if widget_vbox:\n", "                    new_widgets.append(widget_vbox)\n", "                    block_config['param_widgets'][param_name] = widget_vbox.children[0]\n", "        \n", "        params_box_for_block.children = new_widgets\n", "        self._update_registry()\n", "\n", "    def _on_block_type_change(self, change, step_id, block_id):\n", "        if change['name'] == 'value' and change['type'] == 'change':\n", "            step_config = next(c for c in self.calculation_step_configs if c['id'] == step_id)\n", "            block_config = next(b for b in step_config['logic_blocks'] if b['id'] == block_id)\n", "            self._populate_params_for_block(change['new'], block_config['params_box'], step_id, block_id)\n", "            # _populate_params_for_block will call _update_registry\n", "\n", "    def _trigger_registry_update(self, change, entity_type, step_id, block_id=None, param_name_key=None, param_group_key=None):\n", "        # Generic trigger for registry update, ensures it's called on value changes\n", "        if change['name'] == 'value' and change['type'] == 'change':\n", "            self._update_registry()\n", "            \n", "    def _build_params_for_single_block(self, block_config_dict):\n", "        \"\"\" Helper to extract params for one logic block \"\"\"\n", "        params_data = {}\n", "        block_calc_type = block_config_dict['type_widget'].value\n", "        block_type_schema = self.calculation_schemas.get(block_calc_type, {})\n", "        block_type_params_schema = block_type_schema.get('params', {})\n", "\n", "        for param_key, widget_or_dict in block_config_dict['param_widgets'].items():\n", "            param_schema = block_type_params_schema.get(param_key, {}) # Schema for current param in this block\n", "            \n", "            if isinstance(widget_or_dict, dict): # Group\n", "                group_data = {}\n", "                sub_param_schema_group = param_schema.get('sub_params', {})\n", "                for sub_param_key, sub_widget_instance in widget_or_dict.items():\n", "                    raw_sub_value = sub_widget_instance.value\n", "                    sub_param_item_schema = sub_param_schema_group.get(sub_param_key, {})\n", "                    is_optional_sub = sub_param_item_schema.get(\"optional\", False)\n", "                    if is_optional_sub and (raw_sub_value is None or (isinstance(raw_sub_value, str) and not raw_sub_value.strip())):\n", "                        continue\n", "                    \n", "                    sub_final_value = raw_sub_value\n", "                    current_sub_param_type = sub_param_item_schema.get('type')\n", "\n", "                    if current_sub_param_type == 'variable_name' and self.user_globals:\n", "                        if isinstance(raw_sub_value, str) and raw_sub_value.strip():\n", "                            var_name_str = raw_sub_value.strip()\n", "                            resolved_object = self.user_globals.get(var_name_str)\n", "                            sub_final_value = resolved_object if resolved_object is not None else var_name_str\n", "                    # Specific handling for 'on' field within 'prior_merge' group\n", "                    elif param_key == \"prior_merge\" and sub_param_key == \"on\" and \\\n", "                            current_sub_param_type == 'text' and isinstance(raw_sub_value, str):\n", "                        sub_final_value = [col.strip() for col in raw_sub_value.split(',') if col.strip()]\n", "                    elif isinstance(sub_widget_instance, widgets.Dropdown) and isinstance(raw_sub_value, bool):\n", "                        sub_final_value = raw_sub_value # Store boolean directly\n", "                    else: # Default stringification logic (simplified)\n", "                        if sub_final_value is not None and not isinstance(sub_final_value, (str, bool, int, float, list, dict)) and \\\n", "                           not (current_sub_param_type == 'variable_name' and sub_final_value is not raw_sub_value): # Avoid re-stringifying resolved objects\n", "                             sub_final_value = str(raw_sub_value)\n", "                    group_data[sub_param_key] = sub_final_value\n", "                \n", "                # Add group if it has content OR if the group itself is not marked optional in its parent schema\n", "                if group_data or not param_schema.get(\"optional\", False):\n", "                     params_data[param_key] = group_data\n", "                elif param_schema.get(\"optional\", False) and not group_data : # explicit skip if optional and empty\n", "                    pass # Do not add empty optional groups\n", "\n", "            elif hasattr(widget_or_dict, 'value'): # Single widget\n", "                raw_value = widget_or_dict.value\n", "                is_optional = param_schema.get(\"optional\", False)\n", "                if is_optional and (raw_value is None or (isinstance(raw_value, str) and not str(raw_value).strip())):\n", "                    continue\n", "                \n", "                current_param_final_value = raw_value\n", "                current_param_type = param_schema.get('type')\n", "\n", "                if current_param_type == 'variable_name' and self.user_globals:\n", "                    if isinstance(raw_value, str) and raw_value.strip():\n", "                        var_name_str = raw_value.strip()\n", "                        resolved_object = self.user_globals.get(var_name_str)\n", "                        current_param_final_value = resolved_object if resolved_object is not None else var_name_str\n", "                elif isinstance(widget_or_dict, widgets.Dropdown) and isinstance(raw_value, bool):\n", "                    current_param_final_value = raw_value # Store boolean directly\n", "                else: # Default stringification (simplified)\n", "                    is_resolved_object = (current_param_type == 'variable_name' and \\\n", "                                           current_param_final_value is not raw_value and raw_value is not None) # Check if it was resolved\n", "                    if not isinstance(current_param_final_value, (bool, list, dict)) and not is_resolved_object:\n", "                        if current_param_final_value is not None and not isinstance(current_param_final_value, (str, int, float)):\n", "                            current_param_final_value = str(raw_value)\n", "                        elif current_param_final_value is None: # Ensure None remains None\n", "                             current_param_final_value = None\n", "                params_data[param_key] = current_param_final_value\n", "        return params_data\n", "\n", "    def _update_registry(self):\n", "        new_registry = {}\n", "        for step_config in self.calculation_step_configs: # Iterate main UI steps (scenarios)\n", "            step_name = step_config['name_widget'].value.strip()\n", "            if not step_name:\n", "                step_name = f\"unnamed_scenario_{step_config['id']}\"\n", "\n", "            # Combine parameters from all logic blocks within this step\n", "            combined_params_for_step = {}\n", "            for block_config in step_config['logic_blocks']:\n", "                params_from_block = self._build_params_for_single_block(block_config)\n", "                combined_params_for_step.update(params_from_block) # Merge dicts\n", "            \n", "            new_registry[step_name] = combined_params_for_step\n", "        \n", "        self.registry = new_registry\n", "        self.registry_output_display.value = self._format_registry_for_display(self.registry)\n", "\n", "    def _on_remove_logic_block_clicked(self, b, step_id, block_id):\n", "        step_config = next((c for c in self.calculation_step_configs if c['id'] == step_id), None)\n", "        if not step_config: return\n", "\n", "        block_to_remove = next((blk for blk in step_config['logic_blocks'] if blk['id'] == block_id), None)\n", "        if not block_to_remove: return\n", "\n", "        step_config['logic_blocks'].remove(block_to_remove) # Remove from config\n", "\n", "        # Remove UI from the VBox container for blocks\n", "        new_block_children = [child for child in step_config['blocks_container_vbox'].children if getattr(child, 'block_id_tag', None) != block_id]\n", "        step_config['blocks_container_vbox'].children = tuple(new_block_children)\n", "        \n", "        self._update_registry()\n", "\n", "\n", "    def _add_logic_block_ui_to_step(self, b, step_id): # 'b' is the button clicked\n", "        step_config = next((c for c in self.calculation_step_configs if c['id'] == step_id), None)\n", "        if not step_config: return\n", "\n", "        block_id = str(uuid.uuid4()) # Unique ID for this logic block\n", "\n", "        block_type_widget = widgets.Dropdown(\n", "            options=list(self.calculation_schemas.keys()), \n", "            description=\"Logic Type:\",\n", "            style=STYLE_DESCRIPTION_WIDTH_MEDIUM, layout=LAYOUT_FULL_WIDTH\n", "        )\n", "        block_params_box = widgets.VBox([], layout=widgets.Layout(margin='5px 0 0 15px')) # Indent params for block\n", "\n", "        block_config_data = {\n", "            'id': block_id,\n", "            'type_widget': block_type_widget,\n", "            'params_box': block_params_box,\n", "            'param_widgets': {} # To be filled by _populate_params_for_block\n", "        }\n", "        step_config['logic_blocks'].append(block_config_data)\n", "\n", "        # Observer for block's type change\n", "        block_type_cb = functools.partial(self._on_block_type_change, step_id=step_id, block_id=block_id)\n", "        block_type_widget.observe(block_type_cb, names='value')\n", "\n", "        remove_block_button = widgets.Button(\n", "            description=\"Remove This Logic Block\", button_style='warning', \n", "            icon='minus-circle', layout=widgets.Layout(width='auto', margin='5px 0 0 auto') # Align right\n", "        )\n", "        remove_block_cb = functools.partial(self._on_remove_logic_block_clicked, step_id=step_id, block_id=block_id)\n", "        remove_block_button.on_click(remove_block_cb)\n", "        \n", "        # Header for the block (type selector and remove button)\n", "        block_header_box = widgets.HBox([block_type_widget, remove_block_button])\n", "        block_ui_vbox = widgets.VBox( # Contains header and params for one block\n", "            [block_header_box, block_params_box],\n", "            layout=widgets.Layout(border=BLOCK_BORDER, padding=BLOCK_PADDING, margin=BLOCK_MARGIN)\n", "        )\n", "        block_ui_vbox.block_id_tag = block_id # For UI removal\n", "\n", "        # Add this new block's UI to the step's container for blocks\n", "        current_block_children = list(step_config['blocks_container_vbox'].children)\n", "        current_block_children.append(block_ui_vbox)\n", "        step_config['blocks_container_vbox'].children = tuple(current_block_children)\n", "        \n", "        # Initial population of params for the default type selected in block_type_widget\n", "        self._populate_params_for_block(block_type_widget.value, block_params_box, step_id, block_id)\n", "        # _populate_params_for_block will call _update_registry\n", "\n", "    def _on_remove_step_clicked(self, b, step_id): # For removing a whole scenario (accordion pane)\n", "        # This function remains largely the same as your last working version\n", "        config_to_remove = next((c for c in self.calculation_step_configs if c['id'] == step_id), None)\n", "        idx_to_remove = -1\n", "        for i, child_vbox in enumerate(self.calculation_steps_accordion.children):\n", "            if hasattr(child_vbox, 'calculation_id_tag') and child_vbox.calculation_id_tag == step_id:\n", "                idx_to_remove = i\n", "                break\n", "        \n", "        if idx_to_remove != -1 and config_to_remove:\n", "            self.calculation_step_configs.remove(config_to_remove)\n", "            new_children = list(self.calculation_steps_accordion.children)\n", "            new_children.pop(idx_to_remove)\n", "            self.calculation_steps_accordion.children = tuple(new_children)\n", "            \n", "            for i, child in enumerate(self.calculation_steps_accordion.children): # Re-title\n", "                current_step_config = next((c for c in self.calculation_step_configs if c['id'] == child.calculation_id_tag), None)\n", "                if current_step_config:\n", "                    title = current_step_config['name_widget'].value.strip() or f\"Scenario (ID: {current_step_config['id']})\"\n", "                    self.calculation_steps_accordion.set_title(i, title)\n", "            \n", "            if not self.calculation_steps_accordion.children:\n", "                self.calculation_steps_accordion.selected_index = None\n", "            elif self.calculation_steps_accordion.selected_index is not None and \\\n", "                 self.calculation_steps_accordion.selected_index >= len(self.calculation_steps_accordion.children):\n", "                 self.calculation_steps_accordion.selected_index = len(self.calculation_steps_accordion.children) - 1\n", "            \n", "            self._update_registry()\n", "\n", "\n", "    def _update_accordion_title(self, change, accordion_idx, step_name_widget): # Same\n", "        title_to_set = step_name_widget.value.strip()\n", "        if not title_to_set:\n", "            # Find the calculation_id associated with this step_name_widget to create a default title\n", "            assoc_config = next((c for c in self.calculation_step_configs if c['name_widget'] == step_name_widget), None)\n", "            calc_id_for_title = assoc_config['id'] if assoc_config else accordion_idx +1 # Fallback ID\n", "            title_to_set = f\"<PERSON><PERSON>rio (ID: {calc_id_for_title})\"\n", "        if accordion_idx < len(self.calculation_steps_accordion.children): # Check index validity\n", "            self.calculation_steps_accordion.set_title(accordion_idx, title_to_set)\n", "\n", "    def _add_calculation_step_ui(self, b=None): # Adds a new Scenario (accordion pane)\n", "        calculation_id = self._get_next_step_id()\n", "        \n", "        # Name for the whole scenario (e.g., \"s1\", \"base\")\n", "        scenario_name_widget = widgets.Text(\n", "            description=\"Scenario Name:\", placeholder=\"e.g., base_case_scenario\",\n", "            style=STYLE_DESCRIPTION_WIDTH, layout=LAYOUT_FULL_WIDTH\n", "        )\n", "        \n", "        # Container for multiple logic blocks\n", "        blocks_container_vbox = widgets.VBox([])\n", "        \n", "        # Button to add a new logic block to this scenario\n", "        add_logic_block_button = widgets.Button(\n", "            description=\"Add Logic Block to Scenario\", icon='plus-circle', # Your \"Add Scenario\" button\n", "            button_style='primary', layout=LAYOUT_BUTTON # Blue button\n", "        )\n", "        add_logic_block_cb = functools.partial(self._add_logic_block_ui_to_step, step_id=calculation_id)\n", "        add_logic_block_button.on_click(add_logic_block_cb)\n", "\n", "        step_config_data = {\n", "            'id': calculation_id, \n", "            'name_widget': scenario_name_widget,\n", "            'blocks_container_vbox': blocks_container_vbox,\n", "            'logic_blocks': [] # List to store configurations of added logic blocks\n", "        }\n", "        self.calculation_step_configs.append(step_config_data)\n", "\n", "        # Observer for scenario name change (triggers registry update for the key)\n", "        # Also updates accordion title\n", "        name_cb = functools.partial(self._trigger_registry_update, entity_type='step_name', step_id=calculation_id)\n", "        scenario_name_widget.observe(name_cb, names='value')\n", "        \n", "        remove_step_button = widgets.Button( # To remove the whole scenario (accordion pane)\n", "            description=\"Remove This Scenario\", button_style='danger', icon='trash', layout=LAYOUT_BUTTON\n", "        )\n", "        remove_cb = functools.partial(self._on_remove_step_clicked, calculation_id=calculation_id)\n", "        remove_step_button.on_click(remove_cb)\n", "        \n", "        # UI for one accordion pane (one scenario)\n", "        step_ui_vbox = widgets.VBox(\n", "            [scenario_name_widget, add_logic_block_button, blocks_container_vbox, remove_step_button],\n", "            layout=widgets.Layout(border=SECTION_BORDER, padding=SECTION_PADDING, margin='5px 0')\n", "        )\n", "        step_ui_vbox.calculation_id_tag = calculation_id # For removal logic\n", "        \n", "        current_children = list(self.calculation_steps_accordion.children)\n", "        current_children.append(step_ui_vbox)\n", "        self.calculation_steps_accordion.children = tuple(current_children)\n", "        \n", "        accordion_idx = len(self.calculation_steps_accordion.children) - 1\n", "        \n", "        initial_title = scenario_name_widget.placeholder or f\"<PERSON><PERSON><PERSON> (ID: {calculation_id})\"\n", "        self.calculation_steps_accordion.set_title(accordion_idx, initial_title)\n", "        \n", "        title_update_cb = functools.partial(self._update_accordion_title, accordion_idx=accordion_idx, step_name_widget=scenario_name_widget)\n", "        scenario_name_widget.observe(title_update_cb, names='value')\n", "        \n", "        # Optionally add one default logic block here or leave it to the user\n", "        # self._add_logic_block_ui_to_step(None, calculation_id) # Example: add one block by default\n", "        \n", "        self.calculation_steps_accordion.selected_index = accordion_idx\n", "        self._update_registry() # Ensure registry is updated after adding a new step\n", "\n", "    def _get_registry_dict_button_clicked(self, b): # Same\n", "        with self.output_area_for_dict_print:\n", "            clear_output(wait=True)\n", "            print(\"Current Registry Python Dictionary (also available as builder.registry):\")\n", "            import pprint\n", "            pprint.pprint(self.registry)\n", "\n", "    def build(self): # Same structure, but \"Add New Calculation Step\" now means adding a scenario config UI\n", "        if self.full_ui:\n", "            display(self.full_ui)\n", "            return\n", "        \n", "        add_scenario_button = widgets.Button( # This button adds a new accordion pane for a scenario\n", "            description=\"Add New Scenario Configuration\", icon='tasks',\n", "            button_style='success', layout=LAYOUT_BUTTON\n", "        )\n", "        add_scenario_button.on_click(self._add_calculation_step_ui)\n", "        \n", "        get_registry_button = widgets.Button(\n", "            description=\"Print Registry Dict\", icon='print',\n", "            button_style='info', layout=LAYOUT_BUTTON\n", "        )\n", "        get_registry_button.on_click(self._get_registry_dict_button_clicked)\n", "        \n", "        ui_title = widgets.HTML(\"<h2>Pipeline Scenario Registry Builder</h2><hr>\") # Title update\n", "        \n", "        self.full_ui = widgets.VBox([\n", "            ui_title,\n", "            add_scenario_button,\n", "            self.calculation_steps_accordion,\n", "            self.registry_output_display,\n", "            get_registry_button,\n", "            self.output_area_for_dict_print\n", "        ], layout=widgets.Layout(width='80%', margin='auto', padding=SECTION_PADDING, # Wider\n", "                                 border='1px solid #A0A0A0', background_color='#f9f9f9'))\n", "        \n", "        if not self.calculation_step_configs: # Add one scenario config UI by default\n", "             self._add_calculation_step_ui()\n", "        \n", "        display(self.full_ui)"]}, {"cell_type": "code", "execution_count": 5, "id": "efae2dfd", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f674fd59f8d74b6badd6674bb98c3fcf", "version_major": 2, "version_minor": 0}, "text/plain": ["VBox(children=(HTML(value='<h2>Pipeline Scenario Registry Builder</h2><hr>'), Button(button_style='success', d…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["builder = RegistryBuilder(user_globals=globals()) # Pass the notebook's globals\n", "\n", "builder.build()"]}, {"cell_type": "code", "execution_count": null, "id": "df898934", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "datascience", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}