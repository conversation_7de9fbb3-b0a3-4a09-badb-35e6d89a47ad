{"cells": [{"cell_type": "code", "execution_count": null, "id": "486279cc", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "\n", "# Set reproducibility\n", "np.random.seed(42)\n", "THR = 1.2\n", "TAX_RATE = 0.25\n", "\n", "# Create a date range\n", "dates = pd.date_range(start=\"2020-01-01\", end=\"2023-12-31\", freq=\"D\")\n", "\n", "# Simulate a price using a random walk\n", "price = 100 + np.cumsum(np.random.normal(0, 1, len(dates)))\n", "\n", "# Simulate dividends: zero most days, with occasional dividends\n", "dividends = np.zeros(len(dates))\n", "dividend_days = np.random.choice(len(dates), size=12, replace=False)  # 12 random dividend days\n", "dividends[dividend_days] = np.round(np.random.uniform(0.1, 1.0, size=12), 2)\n", "\n", "# Build the DataFrame\n", "df = pd.DataFrame({\n", "    \"price\": price,\n", "    \"dividends\": dividends\n", "}, index=dates)\n"]}, {"cell_type": "code", "execution_count": 23, "id": "1bf155c8", "metadata": {}, "outputs": [], "source": ["df[\"dividends_per_annum\"] = df.groupby(df.index.year)[\"dividends\"].cumsum()\n", "\n", "df[\"dividends_per_annum_taxed\"] = df[\"dividends_per_annum\"].mask(\n", "    df[\"dividends_per_annum\"] > THR,\n", "    THR + (df[\"dividends_per_annum\"] - THR) * (1 - TAX_RATE)\n", ")\n", "\n", "df[\"tax_paid\"] = df[\"dividends_per_annum\"] - df[\"dividends_per_annum_taxed\"]\n", "df[\"tax_paid_daily\"] = df[\"tax_paid\"].diff().fillna(df[\"tax_paid\"])\n", "df[\"dividends_net\"] = df[\"dividends\"] - df[\"tax_paid_daily\"]\n"]}, {"cell_type": "markdown", "id": "f8e923b8", "metadata": {}, "source": ["---"]}, {"cell_type": "code", "execution_count": 1, "id": "14dd5e1e", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from numba import njit, types\n", "\n", "\n", "df = pd.read_csv(\"/home/<USER>/projects/python/reelstonks/assets/data/AAPL_20050705_20250705_1d.csv\", index_col=\"Date\", parse_dates=True)"]}, {"cell_type": "code", "execution_count": 6, "id": "60934327", "metadata": {}, "outputs": [], "source": ["# Define the signature for the njit function\n", "signature = types.Tuple((\n", "    types.float64[:], types.float64[:], types.float64[:],\n", "    types.float64[:], types.float64[:]\n", "))(\n", "    types.float64[:], types.float64[:], types.float64,\n", "    types.boolean[:], types.boolean[:], types.float64,\n", "    types.float64, types.boolean\n", ")\n", "\n", "\n", "@njit(signature, cache=True)\n", "def _invest(\n", "    price: np.n<PERSON>ray, \n", "    dividends: np.n<PERSON><PERSON>, \n", "    investment_amount: float, \n", "    investment_periods: np.n<PERSON><PERSON>,\n", "    yearly_periods: np.n<PERSON><PERSON>,\n", "    tax_rate: float, \n", "    tax_free_return_threshold_per_annu: float,\n", "    reinvest_dividends: bool,\n", "    ) -> np.ndarray:\n", "    \n", "    n = len(price)\n", "\n", "    invested_cash = np.zeros(n)\n", "    number_of_stocks = np.zeros(n)\n", "    paid_gross_dividends = np.zeros(n)\n", "    paid_net_dividends = np.zeros(n)\n", "    dividends_for_wealth = np.zeros(n)\n", "\n", "    wealth = np.zeros(n)\n", "\n", "    _dividends_per_annum = 0\n", "\n", "    for i in range(n):\n", "\n", "        # Get number of stocks held the previous day\n", "        prev_stocks = number_of_stocks[i - 1] if i > 0 else 0.0\n", "\n", "        # Check if we are in a new year\n", "        if yearly_periods[i]:\n", "            # Reset dividends per annum\n", "            _dividends_per_annum = 0\n", "\n", "        # Compute gross dividends based on dividends paid today and stocks held yesterday\n", "        paid_gross_dividends[i] = prev_stocks * dividends[i]\n", "\n", "        # Get dividends before and after today\n", "        dividends_before_today = _dividends_per_annum\n", "        dividends_after_today = dividends_before_today + paid_gross_dividends[i]\n", "\n", "        # Define taxable amount if dividends after today exceed the threshold\n", "        taxable_amount = 0.0\n", "        if dividends_after_today > tax_free_return_threshold_per_annu:\n", "            # Calculate the portion of this dividend that is over the threshold\n", "            taxable_amount = dividends_after_today - max(dividends_before_today, tax_free_return_threshold_per_annu)\n", "\n", "        # Calculate tax paid today on the taxable amount\n", "        tax_paid = taxable_amount * tax_rate\n", "        # Calculate net dividends after tax\n", "        paid_net_dividends[i] = paid_gross_dividends[i] - tax_paid\n", "        # Update dividends per annum\n", "        _dividends_per_annum = dividends_after_today \n", "\n", "        # Check if we have reached the tax free return threshold in dividends and apply taxes if necessary\n", "        if _dividends_per_annum >= tax_free_return_threshold_per_annu:\n", "            paid_net_dividends[i] = paid_gross_dividends[i] * (1 - tax_rate)\n", "        else:\n", "            paid_net_dividends[i] = paid_gross_dividends[i]\n", "\n", "        # Reinvest dividends \n", "        if reinvest_dividends:\n", "            # Update investment amount with reinvested dividends\n", "            _investment_amount = investment_amount + paid_net_dividends[i]\n", "            # Dividends are reinvested, so we don't add them to wealth\n", "            dividends_for_wealth[i] = 0\n", "        else:\n", "            # No reinvestment, so investment amount stays the same\n", "            _investment_amount = investment_amount\n", "            # Dividends are not reinvested, so we add them to wealth\n", "            dividends_for_wealth[i] = paid_net_dividends[i]\n", "\n", "        # Check if we are on an investment day\n", "        if investment_periods[i]:\n", "            # Store investment amount at investment date\n", "            invested_cash[i] = _investment_amount\n", "            # Compute number of stocks bought on investment days and add to previous stocks\n", "            number_of_stocks[i] = _investment_amount / price[i] + prev_stocks\n", "        else:\n", "            # Not an investment day, so no new stocks bought\n", "            number_of_stocks[i] = prev_stocks\n", "\n", "        # Compute wealth\n", "        wealth[i] = (number_of_stocks[i] * price[i]) + dividends_for_wealth[i]\n", "\n", "    return (\n", "        wealth, invested_cash, number_of_stocks, paid_gross_dividends, paid_net_dividends\n", "    ) \n", "\n", "\n", "def invest(df: pd.DataFrame) -> pd.DataFrame:\n", "\n", "    target_dates = pd.date_range(\n", "        start=df.index[0],\n", "        end=df.index[-1],\n", "        freq=pd.DateOffset(months=1),\n", "    )\n", "    investment_indices = df.index.searchsorted(target_dates)\n", "    investment_indices = investment_indices[\n", "        investment_indices < len(df.index)\n", "    ]\n", "    investment_dates = df.index[investment_indices].unique()\n", "    investment_periods = df.index.isin(investment_dates)\n", "\n", "    yearly_periods = ~df.index.strftime(\"%Y\").duplicated()\n", "\n", "    (\n", "        wealth, \n", "        invested_cash, \n", "        number_of_stocks, \n", "        paid_gross_dividends, \n", "        paid_net_dividends\n", "    ) = _invest(\n", "        price=df[\"Close\"].values,\n", "        dividends=df[\"Dividends\"].values,\n", "        investment_amount=1_000,\n", "        investment_periods=investment_periods,\n", "        yearly_periods=yearly_periods,\n", "        tax_rate=0.26375,\n", "        tax_free_return_threshold_per_annu=1_000,\n", "        reinvest_dividends=False,\n", "    )\n", "\n", "    wealth_df = pd.DataFrame(\n", "        {\n", "            \"Wealth\": wealth,\n", "            \"Invested Cash\": invested_cash,\n", "            \"Number of Stocks\": number_of_stocks,\n", "            \"Paid Gross Dividends\": paid_gross_dividends,\n", "            \"Paid Net Dividends\": paid_net_dividends,\n", "        },\n", "        index=df.index,\n", "    )\n", "\n", "    return pd.concat([df, wealth_df], axis=1)\n"]}, {"cell_type": "code", "execution_count": 7, "id": "5ad43b6d", "metadata": {}, "outputs": [], "source": ["df = invest(df)"]}, {"cell_type": "code", "execution_count": 8, "id": "2c2f6cf6", "metadata": {}, "outputs": [], "source": ["true_array = np.full(10, True, dtype=bool)\n"]}, {"cell_type": "code", "execution_count": 13, "id": "ac019af4", "metadata": {}, "outputs": [], "source": ["df = pd.read_csv(\"/home/<USER>/projects/python/reelstonks/assets/data/MSFT_20050705_20250705_1d.csv\")"]}, {"cell_type": "code", "execution_count": 15, "id": "4961a82f", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(28.520000000000003)"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["df.Dividends.sum()"]}, {"cell_type": "code", "execution_count": 1, "id": "bfa29721", "metadata": {}, "outputs": [], "source": ["import os\n", "from telegram import Update\n", "from telegram.ext import Application, CommandHandler, MessageHandler, filters, ConversationHandler, ContextTypes\n", "\n", "\n", "# Define conversation states\n", "FIRST_QUESTION, SECOND_QUESTION, END = range(3)\n", "\n", "async def start(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:\n", "    \"\"\"Starts the conversation.\"\"\"\n", "    await update.message.reply_text(\n", "        \"Hi! I'm a simple bot. What's your favorite color?\"\n", "    )\n", "    return FIRST_QUESTION\n", "\n", "\n", "async def handle_first_response(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:\n", "    \"\"\"Stores the first response and asks the second question.\"\"\"\n", "    user_response = update.message.text\n", "    context.user_data['favorite_color'] = user_response\n", "    await update.message.reply_text(\n", "        f\"Oh, {user_response} is a nice color! Now, what's your favorite animal?\"\n", "    )\n", "    return SECOND_QUESTION\n", "\n", "\n", "async def handle_second_response(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:\n", "    \"\"\"Stores the second response and provides a summary.\"\"\"\n", "    user_response = update.message.text\n", "    favorite_color = context.user_data.get('favorite_color', 'not specified')\n", "    favorite_animal = user_response\n", "\n", "    output_message = (\n", "        f\"Thanks for sharing! So, your favorite color is {favorite_color} \"\n", "        f\"and your favorite animal is {favorite_animal}.\"\n", "    )\n", "    await update.message.reply_text(output_message)\n", "\n", "    return ConversationHandler.END # End the conversation\n", "\n", "\n", "async def cancel(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:\n", "    \"\"\"Cancels and ends the conversation.\"\"\"\n", "    await update.message.reply_text(\n", "        \"Okay, bye! You can start again with /start.\"\n", "    )\n", "    return ConversationHandler.END\n", "\n", "\n", "def main() -> None:\n", "    \"\"\"Run the bot.\"\"\"\n", "    application = Application.builder().token(os.environ.get(\"TELEGRAM_BOT_TOKEN\", None)).build()\n", "\n", "    conv_handler = ConversationHandler(\n", "        entry_points=[<PERSON><PERSON><PERSON><PERSON>(\"start\", start)],\n", "        states={\n", "            FIRST_QUESTION: [MessageHand<PERSON>(filters.TEXT & ~filters.COMMAND, handle_first_response)],\n", "            SECOND_QUESTION: [MessageHand<PERSON>(filters.TEXT & ~filters.COMMAND, handle_second_response)],\n", "        },\n", "        fallbacks=[<PERSON><PERSON><PERSON><PERSON>(\"cancel\", cancel)],\n", "    )\n", "\n", "    application.add_handler(conv_handler)\n", "\n", "    application.run_polling(allowed_updates=Update.ALL_TYPES)\n"]}, {"cell_type": "code", "execution_count": 2, "id": "c3d63134", "metadata": {}, "outputs": [{"ename": "RuntimeError", "evalue": "Cannot close a running event loop", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON>unt<PERSON>E<PERSON><PERSON>\u001b[39m                              <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/datascience/lib/python3.13/site-packages/telegram/ext/_application.py:1047\u001b[39m, in \u001b[36mApplication.__run\u001b[39m\u001b[34m(self, updater_coroutine, stop_signals, bootstrap_retries, close_loop)\u001b[39m\n\u001b[32m   1046\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m1047\u001b[39m     \u001b[43mloop\u001b[49m\u001b[43m.\u001b[49m\u001b[43mrun_until_complete\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_bootstrap_initialize\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmax_retries\u001b[49m\u001b[43m=\u001b[49m\u001b[43mbootstrap_retries\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1048\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m.post_init:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/datascience/lib/python3.13/asyncio/base_events.py:701\u001b[39m, in \u001b[36mBaseEventLoop.run_until_complete\u001b[39m\u001b[34m(self, future)\u001b[39m\n\u001b[32m    700\u001b[39m \u001b[38;5;28mself\u001b[39m._check_closed()\n\u001b[32m--> \u001b[39m\u001b[32m701\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_check_running\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    703\u001b[39m new_task = \u001b[38;5;129;01mnot\u001b[39;00m futures.isfuture(future)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/datascience/lib/python3.13/asyncio/base_events.py:637\u001b[39m, in \u001b[36mBaseEventLoop._check_running\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    636\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m.is_running():\n\u001b[32m--> \u001b[39m\u001b[32m637\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mRuntimeError\u001b[39;00m(\u001b[33m'\u001b[39m\u001b[33mThis event loop is already running\u001b[39m\u001b[33m'\u001b[39m)\n\u001b[32m    638\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m events._get_running_loop() \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "\u001b[31mRuntimeError\u001b[39m: This event loop is already running", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[31m<PERSON>unt<PERSON>E<PERSON><PERSON>\u001b[39m                              <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/datascience/lib/python3.13/site-packages/telegram/ext/_application.py:1072\u001b[39m, in \u001b[36mApplication.__run\u001b[39m\u001b[34m(self, updater_coroutine, stop_signals, bootstrap_retries, close_loop)\u001b[39m\n\u001b[32m   1071\u001b[39m         loop.run_until_complete(\u001b[38;5;28mself\u001b[39m.post_stop(\u001b[38;5;28mself\u001b[39m))\n\u001b[32m-> \u001b[39m\u001b[32m1072\u001b[39m \u001b[43mloop\u001b[49m\u001b[43m.\u001b[49m\u001b[43mrun_until_complete\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mshutdown\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1073\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m.post_shutdown:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/datascience/lib/python3.13/asyncio/base_events.py:701\u001b[39m, in \u001b[36mBaseEventLoop.run_until_complete\u001b[39m\u001b[34m(self, future)\u001b[39m\n\u001b[32m    700\u001b[39m \u001b[38;5;28mself\u001b[39m._check_closed()\n\u001b[32m--> \u001b[39m\u001b[32m701\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_check_running\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    703\u001b[39m new_task = \u001b[38;5;129;01mnot\u001b[39;00m futures.isfuture(future)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/datascience/lib/python3.13/asyncio/base_events.py:637\u001b[39m, in \u001b[36mBaseEventLoop._check_running\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    636\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m.is_running():\n\u001b[32m--> \u001b[39m\u001b[32m637\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mRuntimeError\u001b[39;00m(\u001b[33m'\u001b[39m\u001b[33mThis event loop is already running\u001b[39m\u001b[33m'\u001b[39m)\n\u001b[32m    638\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m events._get_running_loop() \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "\u001b[31mRuntimeError\u001b[39m: This event loop is already running", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[31m<PERSON>unt<PERSON>E<PERSON><PERSON>\u001b[39m                              <PERSON><PERSON> (most recent call last)", "\u001b[36m<PERSON>ell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[2]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[43mmain\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[1]\u001b[39m\u001b[32m, line 65\u001b[39m, in \u001b[36mmain\u001b[39m\u001b[34m()\u001b[39m\n\u001b[32m     54\u001b[39m conv_handler = ConversationHandler(\n\u001b[32m     55\u001b[39m     entry_points=[CommandHandler(\u001b[33m\"\u001b[39m\u001b[33mstart\u001b[39m\u001b[33m\"\u001b[39m, start)],\n\u001b[32m     56\u001b[39m     states={\n\u001b[32m   (...)\u001b[39m\u001b[32m     60\u001b[39m     fallbacks=[CommandHandler(\u001b[33m\"\u001b[39m\u001b[33mcancel\u001b[39m\u001b[33m\"\u001b[39m, cancel)],\n\u001b[32m     61\u001b[39m )\n\u001b[32m     63\u001b[39m application.add_handler(conv_handler)\n\u001b[32m---> \u001b[39m\u001b[32m65\u001b[39m \u001b[43mapplication\u001b[49m\u001b[43m.\u001b[49m\u001b[43mrun_polling\u001b[49m\u001b[43m(\u001b[49m\u001b[43mallowed_updates\u001b[49m\u001b[43m=\u001b[49m\u001b[43mUpdate\u001b[49m\u001b[43m.\u001b[49m\u001b[43mALL_TYPES\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/datascience/lib/python3.13/site-packages/telegram/ext/_application.py:837\u001b[39m, in \u001b[36mApplication.run_polling\u001b[39m\u001b[34m(self, poll_interval, timeout, bootstrap_retries, allowed_updates, drop_pending_updates, close_loop, stop_signals)\u001b[39m\n\u001b[32m    834\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34merror_callback\u001b[39m(exc: TelegramError) -> \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m    835\u001b[39m     \u001b[38;5;28mself\u001b[39m.create_task(\u001b[38;5;28mself\u001b[39m.process_error(error=exc, update=\u001b[38;5;28;01mNone\u001b[39;00m))\n\u001b[32m--> \u001b[39m\u001b[32m837\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m__run\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    838\u001b[39m \u001b[43m    \u001b[49m\u001b[43mupdater_coroutine\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mupdater\u001b[49m\u001b[43m.\u001b[49m\u001b[43mstart_polling\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    839\u001b[39m \u001b[43m        \u001b[49m\u001b[43mpoll_interval\u001b[49m\u001b[43m=\u001b[49m\u001b[43mpoll_interval\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    840\u001b[39m \u001b[43m        \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    841\u001b[39m \u001b[43m        \u001b[49m\u001b[43mbootstrap_retries\u001b[49m\u001b[43m=\u001b[49m\u001b[43mbootstrap_retries\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    842\u001b[39m \u001b[43m        \u001b[49m\u001b[43mallowed_updates\u001b[49m\u001b[43m=\u001b[49m\u001b[43mallowed_updates\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    843\u001b[39m \u001b[43m        \u001b[49m\u001b[43mdrop_pending_updates\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdrop_pending_updates\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    844\u001b[39m \u001b[43m        \u001b[49m\u001b[43merror_callback\u001b[49m\u001b[43m=\u001b[49m\u001b[43merror_callback\u001b[49m\u001b[43m,\u001b[49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# if there is an error in fetching updates\u001b[39;49;00m\n\u001b[32m    845\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    846\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstop_signals\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstop_signals\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    847\u001b[39m \u001b[43m    \u001b[49m\u001b[43mbootstrap_retries\u001b[49m\u001b[43m=\u001b[49m\u001b[43mbootstrap_retries\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    848\u001b[39m \u001b[43m    \u001b[49m\u001b[43mclose_loop\u001b[49m\u001b[43m=\u001b[49m\u001b[43mclose_loop\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    849\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/datascience/lib/python3.13/site-packages/telegram/ext/_application.py:1077\u001b[39m, in \u001b[36mApplication.__run\u001b[39m\u001b[34m(self, updater_coroutine, stop_signals, bootstrap_retries, close_loop)\u001b[39m\n\u001b[32m   1075\u001b[39m \u001b[38;5;28;01mfinally\u001b[39;00m:\n\u001b[32m   1076\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m close_loop:\n\u001b[32m-> \u001b[39m\u001b[32m1077\u001b[39m         \u001b[43mloop\u001b[49m\u001b[43m.\u001b[49m\u001b[43mclose\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/datascience/lib/python3.13/asyncio/unix_events.py:70\u001b[39m, in \u001b[36m_UnixSelectorEventLoop.close\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m     69\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mclose\u001b[39m(\u001b[38;5;28mself\u001b[39m):\n\u001b[32m---> \u001b[39m\u001b[32m70\u001b[39m     \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[43mclose\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     71\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m sys.is_finalizing():\n\u001b[32m     72\u001b[39m         \u001b[38;5;28;01mfor\u001b[39;00m sig \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mlist\u001b[39m(\u001b[38;5;28mself\u001b[39m._signal_handlers):\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/datascience/lib/python3.13/asyncio/selector_events.py:101\u001b[39m, in \u001b[36mBaseSelectorEventLoop.close\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m     99\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mclose\u001b[39m(\u001b[38;5;28mself\u001b[39m):\n\u001b[32m    100\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m.is_running():\n\u001b[32m--> \u001b[39m\u001b[32m101\u001b[39m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mRuntimeError\u001b[39;00m(\u001b[33m\"\u001b[39m\u001b[33mCannot close a running event loop\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m    102\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m.is_closed():\n\u001b[32m    103\u001b[39m         \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m\n", "\u001b[31mRuntime<PERSON>rror\u001b[39m: Cannot close a running event loop"]}], "source": ["main()"]}, {"cell_type": "code", "execution_count": 4, "id": "cc2a69f1", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[15:50:02] \u001b[92mAuthenticating browser with cookies\u001b[0m\n", "[15:50:02] Create a chrome browser instance in headless mode\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Attempting to upload video using cookies.txt...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[15:50:04] \u001b[92mAuthenticating browser with cookies\u001b[0m\n", "[15:50:05] Posting \u001b[1m/home/<USER>/projects/python/reelstonks/assets/animations/250708_13/wealth_animation.mp4\u001b[0m\n", "[15:50:05] \u001b[92mNavigating to upload page\u001b[0m\n", "[15:50:06] \u001b[92mUploading video file\u001b[0m\n", "[15:50:08] \u001b[92mRemoving split window\u001b[0m\n", "[15:50:13] \u001b[91mSplit window not found or operation timed out\u001b[0m\n", "[15:50:13] \u001b[92mSetting interactivity settings\u001b[0m\n", "[15:50:18] Failed to set interactivity settings\n", "[15:50:18] \u001b[92mSetting description\u001b[0m\n", "[15:50:19] \u001b[92mClicking the post button\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Failed to set description:  string index out of range\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[15:51:20] Failed to upload /home/<USER>/projects/python/reelstonks/assets/animations/250708_13/wealth_animation.mp4\n", "[15:51:20] Message: \n", "Stacktrace:\n", "#0 0x608b7ccd21da <unknown>\n", "#1 0x608b7c77cab0 <unknown>\n", "#2 0x608b7c7ce6f0 <unknown>\n", "#3 0x608b7c7ce8e1 <unknown>\n", "#4 0x608b7c81cb94 <unknown>\n", "#5 0x608b7c7f41cd <unknown>\n", "#6 0x608b7c819fee <unknown>\n", "#7 0x608b7c7f3f73 <unknown>\n", "#8 0x608b7c7c0aeb <unknown>\n", "#9 0x608b7c7c1751 <unknown>\n", "#10 0x608b7cc96aeb <unknown>\n", "#11 0x608b7cc9a8c9 <unknown>\n", "#12 0x608b7cc7d8c9 <unknown>\n", "#13 0x608b7cc9b488 <unknown>\n", "#14 0x608b7cc6207f <unknown>\n", "#15 0x608b7ccbf888 <unknown>\n", "#16 0x608b7ccbfa66 <unknown>\n", "#17 0x608b7ccd14f6 <unknown>\n", "#18 0x7258b9a9caa4 <unknown>\n", "#19 0x7258b9b29c3c <unknown>\n", "\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ Video uploaded successfully (as private)!\n", "This method is more reliable. You're all set.\n"]}], "source": ["from tiktok_uploader.upload import upload_video\n", "import os # Good practice to build file paths\n", "\n", "# --- Your Details ---\n", "\n", "# The script will use the cookie file, so you don't need a sessionid variable.\n", "FILE_PATH = \"/home/<USER>/projects/python/reelstonks/assets/animations/250708_13/wealth_animation.mp4\" # Make sure this matches your video's filename\n", "COOKIE_FILE = \"/home/<USER>/Downloads/www.tiktok.com_cookies.txt\"\n", "TITLE = \"Stonks only go up, right? 📈 #stonks #finance #investing #memes #reelstonks\"\n", "\n", "\n", "def main():\n", "    \"\"\"\n", "    Uploads a video to TikTok using a cookies file for authentication.\n", "    \"\"\"\n", "    if not os.path.exists(COOKIE_FILE):\n", "        print(f\"❌ Cookie file not found! Please ensure '{COOKIE_FILE}' is in the same folder as the script.\")\n", "        return\n", "        \n", "    if not os.path.exists(FILE_PATH):\n", "        print(f\"❌ Video file not found! Please ensure '{FILE_PATH}' is in the same folder as the script.\")\n", "        return\n", "\n", "    print(\"Attempting to upload video using cookies.txt...\")\n", "\n", "    try:\n", "        upload_video(\n", "            filename=FILE_PATH,\n", "            # This is the important part: tell the uploader to use your file.\n", "            cookies=COOKIE_FILE,\n", "            title=TITLE,\n", "            headless=True,\n", "            privacy_type=\"private\" # Use 'private' for testing!\n", "        )\n", "        print(\"✅ Video uploaded successfully (as private)!\")\n", "        print(\"This method is more reliable. You're all set.\")\n", "\n", "    except Exception as e:\n", "        print(f\"❌ An error occurred: {e}\")\n", "        print(\"If this is an authentication error, try re-downloading your cookies.txt file.\")\n", "\n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "code", "execution_count": 1, "id": "fc1ea5bd", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[18:42:17] \u001b[92mAuthenticating browser with cookies\u001b[0m\n", "[18:42:17] Create a chrome browser instance in headless mode\n", "[18:42:18] \u001b[92mAuthenticating browser with cookies\u001b[0m\n", "[18:42:20] Posting \u001b[1m/home/<USER>/projects/python/reelstonks/assets/animations/250708_16/wealth_animation.mp4\u001b[0m\n", "               with description: \u001b[1mStonks only go up, right? #stonks #finance #investing #memes #reelstonks\u001b[0m\n", "[18:42:20] \u001b[92mNavigating to upload page\u001b[0m\n", "[18:42:21] \u001b[92mUploading video file\u001b[0m\n", "[18:42:23] \u001b[92mRemoving split window\u001b[0m\n", "[18:42:28] \u001b[91mSplit window not found or operation timed out\u001b[0m\n", "[18:42:28] \u001b[92mSetting interactivity settings\u001b[0m\n", "[18:42:33] Failed to set interactivity settings\n", "[18:42:33] \u001b[92mSetting description\u001b[0m\n", "[18:43:02] \u001b[92mClicking the post button\u001b[0m\n", "[18:44:03] Failed to upload /home/<USER>/projects/python/reelstonks/assets/animations/250708_16/wealth_animation.mp4\n", "[18:44:03] Message: \n", "Stacktrace:\n", "#0 0x6168e96e023a <unknown>\n", "#1 0x6168e918aab0 <unknown>\n", "#2 0x6168e91dc6f0 <unknown>\n", "#3 0x6168e91dc8e1 <unknown>\n", "#4 0x6168e922ab94 <unknown>\n", "#5 0x6168e92021cd <unknown>\n", "#6 0x6168e9227fee <unknown>\n", "#7 0x6168e9201f73 <unknown>\n", "#8 0x6168e91ceaeb <unknown>\n", "#9 0x6168e91cf751 <unknown>\n", "#10 0x6168e96a4b4b <unknown>\n", "#11 0x6168e96a8929 <unknown>\n", "#12 0x6168e968b929 <unknown>\n", "#13 0x6168e96a94e8 <unknown>\n", "#14 0x6168e96700df <unknown>\n", "#15 0x6168e96cd8e8 <unknown>\n", "#16 0x6168e96cdac6 <unknown>\n", "#17 0x6168e96df556 <unknown>\n", "#18 0x72fff0c9caa4 <unknown>\n", "#19 0x72fff0d29c3c <unknown>\n", "\n"]}, {"data": {"text/plain": ["[{'path': '/home/<USER>/projects/python/reelstonks/assets/animations/250708_16/wealth_animation.mp4',\n", "  'description': '<PERSON>onks only go up, right? #stonks #finance #investing #memes #reelstonks',\n", "  'schedule': None,\n", "  'product_id': None}]"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from tiktok_uploader.upload import upload_video\n", "\n", "# --- Your Details ---\n", "FILE_PATH = \"/home/<USER>/projects/python/reelstonks/assets/animations/250708_16/wealth_animation.mp4\"\n", "COOKIE_FILE = \"/home/<USER>/Downloads/cookies.txt\"\n", "DESCRIPTION = \"Stonks only go up, right? #stonks #finance #investing #memes #reelstonks\"\n", "\n", "# --- Run the upload ---\n", "upload_video(\n", "    FILE_PATH,\n", "    description=DESCRIPTION,\n", "    cookies=COOKIE_FILE,\n", "    headless=True,\n", "    privacy_type=\"private\"\n", ")"]}, {"cell_type": "code", "execution_count": 6, "id": "29b5f907", "metadata": {}, "outputs": [], "source": ["def generate_description_single_line(music_creator: str | bool, music_link: str | bool) -> str:\n", "    \"\"\"\n", "    Generates a single-line description, joining parts with a space.\n", "\n", "    Args:\n", "        music_creator: The name of the music creator.\n", "        music_link: The link for the music.\n", "\n", "    Returns:\n", "        The formatted single-line description string.\n", "    \"\"\"\n", "    parts = \"\"\"<PERSON>onks only go up!\n", "    \n", "    \"\"\"\n", "\n", "    if music_creator:\n", "        parts += f\"\"\"Music by {music_creator}.\n", "        \"\"\"\n", "\n", "    if music_link:\n", "        parts += f\"\"\"Check out: {music_link}\n", "\n", "        \"\"\"\n", "\n", "    parts += \"#stocks #crypto #finance #investing #reelstonks #stockmarket #money\"\n", "\n", "    return parts"]}, {"cell_type": "code", "execution_count": 7, "id": "5afba80c", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Stonks only go up!\\n\\n    Music by Artist Name.\\n        Check out: https://example.com/music\\n\\n        #stocks #crypto #finance #investing #reelstonks #stockmarket #money'"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["generate_description_single_line(\"Artist Name\", \"https://example.com/music\")"]}, {"cell_type": "code", "execution_count": null, "id": "7710e7d1", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "datascience", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}