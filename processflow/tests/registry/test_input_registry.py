from dataclasses import dataclass
from processflow.decorators import spool
from processflow.decorators.registry import InputRegistry


@spool
def stock_data(
    price: float,
    pe_ratio: float,
    ps_ratio: float,
    peg_ratio: float,
    pb_ratio: float,
) -> InputRegistry:
    """Returns numeric constants from the registry."""


@spool(file="stock.toml")
@dataclass
class StockData:
    """Data class for collection all variables from registry config files."""
    price: float
    pe_ratio: float
    ps_ratio: float
    peg_ratio: float
    pb_ratio: float


def test_spool_toml_function():
    """Test spool decorator with TOML files."""
    # Use a different variable name for the result
    data = stock_data()

    assert hasattr(stock_data, "__registry__")
    assert isinstance(data, InputRegistry)
    assert data.price == 233.33
    assert data.pe_ratio == 35.41
    assert data.ps_ratio == 8.62
    assert data.peg_ratio == 116.31
    assert data.pb_ratio == 52.6
    assert data.__dict__ == {
        "price": 233.33,
        "pe_ratio": 35.41,
        "ps_ratio": 8.62,
        "peg_ratio": 116.31,
        "pb_ratio": 52.6    
    }


def test_spool_toml_class():
    """Test spool decorator with TOML files."""
    # Use a different variable name for the result
    data = StockData()

    assert hasattr(StockData, "__registry__")
    assert isinstance(data, StockData)
    assert data.price == 233.33
    assert data.pe_ratio == 35.41
    assert data.ps_ratio == 8.62
    assert data.peg_ratio == 116.31
    assert data.pb_ratio == 52.6
    assert data.__dict__ == {
        "price": 233.33,
        "pe_ratio": 35.41,
        "ps_ratio": 8.62,
        "peg_ratio": 116.31,
        "pb_ratio": 52.6    
    }
