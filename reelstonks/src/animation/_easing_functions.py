def ease_in_out_quadratic(x):
    """Apply quadratic ease-in-out easing to a normalized time value.

    This easing function creates smooth acceleration at the beginning and
    deceleration at the end of an animation. The function follows a quadratic
    curve that starts slowly, speeds up in the middle, and slows down at the end.

    Args:
        x (float): Normalized time value between 0.0 and 1.0, where 0.0
            represents the start and 1.0 represents the end of the animation.

    Returns:
        float: Eased value between 0.0 and 1.0. The output follows a smooth
            quadratic curve that provides natural-looking animation timing.
    """
    if x < 0.5:
        return 2 * x * x
    return 1 - ((-2 * x + 2) ** 2) / 2


EASING_FUNCTIONS = {
    "ease_in_out_quadratic": ease_in_out_quadratic,
}
