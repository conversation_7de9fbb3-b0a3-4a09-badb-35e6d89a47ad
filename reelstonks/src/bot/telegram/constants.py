"""Constants and configuration for the Telegram bot module."""

import os
from dataclasses import dataclass
from src.logger import get_logger

LOGGER = get_logger(__name__)


@dataclass
class TelegramConfig:
    token: str
    chat_id: str
    enabled: bool
    interactive_menu: bool
    interactive_timeout_minutes: int
    telegram_api_base_url: str = "https://api.telegram.org/bot"
    telegram_request_timeout: int = 10
    messages_per_second: float = 1.0
    messages_per_minute: int = 20


class TelegramConfigLoader:
    """Loads Telegram configuration from environment variables and [telegram] section in config.toml."""

    @staticmethod
    def _load_from_env(telegram_cfg: dict) -> TelegramConfig:
        telegram_token = telegram_cfg.get(
            "token", os.environ.get("TELEGRAM_BOT_TOKEN", None)
        )
        telegram_chat_id = telegram_cfg.get(
            "chat_id", os.environ.get("TELEGRAM_CHAT_ID", None)
        )
        telegram_enabled = telegram_cfg.get("enabled", True)
        telegram_enabled = (
            telegram_enabled
            and telegram_token is not None
            and telegram_chat_id is not None
        )
        interactive_menu_enabled: bool = telegram_cfg.get("interactive_menu", False)
        interactive_timeout_minutes: int = telegram_cfg.get(
            "interactive_timeout_minutes", 5
        )

        return TelegramConfig(
            token=telegram_token,
            chat_id=telegram_chat_id,
            enabled=telegram_enabled,
            interactive_menu=interactive_menu_enabled,
            interactive_timeout_minutes=interactive_timeout_minutes,
        )
