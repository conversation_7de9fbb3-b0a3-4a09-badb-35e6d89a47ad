"""Utility functions for merging configuration data with interactive menu selections."""

from typing import Dict, Any
from copy import deepcopy

from src.logger import get_logger

LOGGER = get_logger(__name__)


def merge_strategy_config(
    base_config: Dict[str, Any], menu_selection_data: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Merge base configuration with interactive menu selection data.

    Args:
        base_config (Dict[str, Any]): Base configuration from config.toml.
        menu_selection_data (Dict[str, Any]): Strategy data from menu selection.

    Returns:
        Dict[str, Any]: Merged configuration dictionary.
    """
    LOGGER.activate()

    # If menu selection indicates to use config file as-is
    if menu_selection_data.get("use_config_file", False):
        LOGGER.info("🔧 Using default configuration from config.toml")
        return base_config

    # Create a deep copy of base config to avoid modifying original
    merged_config = deepcopy(base_config)

    # Replace strategies section with menu selection
    if "strategies" in menu_selection_data:
        merged_config["strategies"] = menu_selection_data["strategies"]
        LOGGER.info(
            f"📋 Replaced strategies with menu selection: {len(menu_selection_data['strategies'])} strategies"
        )

        # Log the selected strategies
        for strategy_id, strategy_data in menu_selection_data["strategies"].items():
            LOGGER.info(
                f"  - {strategy_id}: {strategy_data.get('name', strategy_data.get('ticker', 'Unknown'))}"
            )

    # Merge general configuration if provided
    if "general" in menu_selection_data:
        if "general" not in merged_config:
            merged_config["general"] = {}

        general_overrides = menu_selection_data["general"]
        merged_config["general"].update(general_overrides)

        LOGGER.info(
            f"🔧 Updated general config with {len(general_overrides)} overrides:"
        )
        for key, value in general_overrides.items():
            LOGGER.info(f"  - {key}: {value}")

    # Merge any other configuration overrides
    for key, value in menu_selection_data.items():
        if key not in ["strategies", "use_config_file", "general"]:
            merged_config[key] = value

    return merged_config


def validate_strategy_config(strategy_data: Dict[str, Any]) -> bool:
    """
    Validate strategy configuration data.

    Args:
        strategy_data (Dict[str, Any]): Strategy configuration to validate.

    Returns:
        bool: True if valid, False otherwise.
    """
    required_fields = ["ticker", "name", "reinvest_dividends", "timing"]

    for field in required_fields:
        if field not in strategy_data:
            LOGGER.error(f"❌ Missing required field in strategy: {field}")
            return False

    # Validate timing values
    valid_timings = ["regular", "peaks", "lows"]
    if strategy_data["timing"] not in valid_timings:
        LOGGER.error(
            f"❌ Invalid timing value: {strategy_data['timing']}. Must be one of {valid_timings}"
        )
        return False

    # Validate reinvest_dividends is boolean
    if not isinstance(strategy_data["reinvest_dividends"], bool):
        LOGGER.error(
            f"❌ reinvest_dividends must be boolean, got: {type(strategy_data['reinvest_dividends'])}"
        )
        return False

    return True


def validate_merged_config(config_data: Dict[str, Any]) -> bool:
    """
    Validate the merged configuration data.

    Args:
        config_data (Dict[str, Any]): Configuration data to validate.

    Returns:
        bool: True if valid, False otherwise.
    """
    # Check if strategies exist
    if "strategies" not in config_data:
        LOGGER.error("❌ No strategies found in configuration")
        return False

    strategies = config_data["strategies"]
    if not strategies:
        LOGGER.error("❌ Strategies section is empty")
        return False

    # Validate each strategy
    for strategy_id, strategy_data in strategies.items():
        if not validate_strategy_config(strategy_data):
            LOGGER.error(f"❌ Invalid strategy configuration: {strategy_id}")
            return False

    LOGGER.info(f"✅ Configuration validation passed for {len(strategies)} strategies")
    return True
