"""
This module contains custom Enum classes for various project-specific enumerations.
"""

from enum import Enum


class TimestampFrequencyEnum(str, Enum):
    """Enumeration for allowed timestamp grouping frequencies."""

    YEARLY_MONTHLY = "%Y-%m"
    YEARLY_DAY = "%Y-%d"
    DAILY = "%Y-%m-%d"
    YEARLY = "%Y"


class DictConditionOperator(str, Enum):
    """Defines the logical operators for key condition matching."""

    AND = "and"
    OR = "or"
