# 📈 ReelStonks

<div align="center">

**🎬 Turn Investment "What Ifs" Into Cinematic Reality 🎬**

*Create stunning animated visualizations of investment scenarios with AI-powered random strategies*

[![Python](https://img.shields.io/badge/Python-3.13+-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Telegram](https://img.shields.io/badge/Telegram-Interactive-blue.svg)](https://telegram.org)

</div>

---

## 🌟 What is ReelStonks?

ReelStonks transforms historical market data into **mesmerizing animated visualizations** with intelligent strategy selection. From Apple vs Tesla showdowns to Bitcoin vs Gold battles - discover fascinating investment scenarios through:

- 🎲 **55 Random Strategies** - Global companies, crypto, indices, timing comparisons
- 🤖 **Interactive Telegram Bot** - Smart strategy selection with adjustment options
- 📊 **Stunning Animations** - Glowing charts optimized for social media
- ⚡ **Timing Analysis** - See how investment timing affects wealth
- 🌍 **Global Coverage** - US, European, Chinese companies and markets

## ✨ Key Features

### 🎲 **Smart Strategy System**
- **55 Random Strategies** - From Tesla vs Apple to Bitcoin vs Gold
- **Interactive Adjustments** - Modify strategies before animation
- **Timing Comparisons** - Peak vs Low vs Regular investment timing
- **Global Showdowns** - US vs European vs Chinese market battles

### 🤖 **Telegram Integration**
- **Interactive Bot** - Choose strategies via clickable menus
- **Random-First Flow** - Quick random selection with timeout fallback
- **Strategy Previews** - See full details before creating animations
- **TikTok Descriptions** - Auto-generated engaging descriptions

### 🎨 **Visual & Audio**
- **Glowing Animations** - Cinema-quality charts with smooth transitions
- **Music Integration** - Background music with smart fallback system
- **Social Media Ready** - Optimized for TikTok, Instagram, YouTube
- **Performance Summaries** - Dramatic fade-in tables with key metrics

## 🚀 Quick Start

### Prerequisites

- **Python 3.13+** 🐍
- **FFmpeg** (for video generation) 🎬

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/reelstonks.git
   cd reelstonks
   ```

2. **Install dependencies**
   ```bash
   # Using uv (recommended)
   uv sync

   # Or using pip
   pip install -e .
   ```

3. **Set up project directories**
   ```bash
   # Run the setup script to create necessary directories
   python setup_project.py

   # Or using uv
   uv run setup_project.py
   ```

4. **Install FFmpeg**
   ```bash
   # macOS
   brew install ffmpeg

   # Ubuntu/Debian
   sudo apt update && sudo apt install ffmpeg

   # Windows
   # Download from https://ffmpeg.org/download.html
   ```

### 🎬 Create Your First Animation

**Option 1: Random Strategy (Recommended)**
```bash
uv run main.py
# Bot asks: "Random strategy?" → Click "Yes" → Get instant fascinating scenario!
```

**Option 2: Custom Configuration**
```toml
# config.toml
[strategies.AAPL_timing]
ticker = "AAPL"
name = "Apple (Peak Timing)"
timing = "peaks"

[strategies.AAPL_regular]
ticker = "AAPL"
name = "Apple (Regular DCA)"
timing = "regular"

[general]
years_ago = 10
investment_amount = 500
investment_kind = "monthly"
```

**Option 3: Telegram Interactive**
- Set up Telegram bot token in `config.toml`
- Get interactive menus with 55 random strategies
- Adjust strategies before animation creation

## 🎲 Random Strategies

### 🌟 **Featured Strategy Categories**

**🤖 AI & Tech Battles**
- Nvidia vs AMD (AI Chip Wars)
- Tesla vs Google (Autonomous Driving)
- Meta vs Unity (VR/Metaverse)

**₿ Crypto vs Traditional**
- Bitcoin vs JPMorgan Chase
- Ethereum vs Gold ETF
- Coinbase vs MicroStrategy

**🌍 Global Market Showdowns**
- S&P 500 vs DAX (US vs Germany)
- China A-Shares vs S&P 500
- European Banking (UBS vs ING)

**⚡ Timing Demonstrations**
- Apple: Peak vs Low vs Regular timing
- Tesla: Volatility timing effects
- Bitcoin: Extreme timing scenarios

**🛡️ Defense & Innovation**
- Rheinmetall vs Palantir
- Boeing vs Lockheed Martin
- CrowdStrike vs Palo Alto Networks

### 🎯 **Strategy Configuration**

```toml
# Timing comparison example
[strategies.NVDA_peaks]
ticker = "NVDA"
name = "Nvidia (Peak Timing)"
timing = "peaks"  # Buy at monthly highs

[strategies.NVDA_lows]
ticker = "NVDA"
name = "Nvidia (Low Timing)"
timing = "lows"   # Buy at monthly lows

[general]
years_ago = 6
investment_amount = 400
investment_kind = "monthly"
```

## 🤖 Telegram Bot Features

### 🎲 **Interactive Strategy Selection**

1. **Random-First Flow** - Default timeout selects random strategy
2. **Strategy Preview** - See full details before animation
3. **Adjustment Options**:
   - ✅ **No** - Use strategy as-is
   - ⏭️ **Skip** - Get new random strategy
   - 🔧 **Yes** - Adjust before use

4. **Adjustment Types**:
   - **Add** - Add more investment strategies
   - **Adjust** - Modify timing, amounts, frequencies
   - **Both** - Add strategies and adjust config

### ⚡ **Timing Strategies**

| Timing | Description | Use Case |
|--------|-------------|----------|
| 📈 **Regular** | Standard dollar-cost averaging | Balanced approach |
| ⬆️ **Peaks** | Buy at monthly highs | Worst-case timing |
| ⬇️ **Lows** | Buy at monthly lows | Best-case timing |

### 🎯 **Investment Frequencies**

| Frequency | Description | Random Strategy Examples |
|-----------|-------------|-------------------------|
| 🗓️ **Daily** | Every trading day | GameStop vs AMC (Meme stocks) |
| 📅 **Weekly** | Once per week | Netflix vs Disney (Streaming wars) |
| 🗓️ **Monthly** | Once per month | Apple vs Tesla (Tech titans) |
| 📊 **Quarterly** | Every 3 months | Rheinmetall vs Palantir (Defense vs Tech) |
| 📈 **Yearly** | Once per year | S&P 500 vs DAX (Market indices) |

## 🎵 Music

### 🎼 **Smart Music System**
```toml
# config.toml
[song]
use_song = "epic-music.mp3"  # Preferred track
_use = true                  # Enable/disable music

# assets/music/creators.toml
[epic-music.mp3]
creator = "Music by Artist Name"
start = 14  # Start time in seconds
```

## 📁 File Organization

### 🗂️ **Random Strategy Management**
```
assets/options/random/
├── option1.toml → option55.toml    # Available strategies
├── used/YYYYMMDD/                  # Completed strategies by date
└── skipped/YYYYMMDD/               # Skipped strategies by date
```

### 🏗️ **Project Structure**
```
reelstonks/
├── src/
│   ├── animation/          # Video generation
│   ├── bot/
│   │   ├── telegram/       # Interactive bot system
│   │   └── tiktok/         # TikTok integration
│   ├── data/               # Yahoo Finance fetching
│   └── invest/             # Investment calculations
├── assets/
│   ├── animations/         # Generated videos
│   ├── music/              # Background music + attribution
│   └── options/random/     # 55 random strategies
└── config.toml             # Main configuration
```

## 🎨 Animation Output

### 📊 **Performance Metrics**
- 🏆 Final wealth rankings
- 📈 CAGR (Compound Annual Growth Rate)
- 📊 Volatility measurements
- 💵 Total investment amounts
- ⚡ Timing impact analysis

### 🎬 **Visual Features**
- **Glowing line effects** with smooth transitions
- **Dark theme** optimized for social media
- **Dynamic scaling** adapts to data ranges
- **Fade-in summaries** with dramatic timing
- **Auto-generated titles** based on strategy content

## 🚀 Quick Examples

### Random Strategy (Fastest)
```bash
python main.py
# → Random strategy selected automatically
# → Animation created in ~30 seconds
```

### Telegram Interactive
```bash
# Set telegram.enabled = true in config.toml
python main.py
# → Get menu with 55 random strategies
# → Preview, adjust, or skip strategies
# → Create custom combinations
```

### Manual Configuration
```toml
[strategies.timing_demo]
ticker = "TSLA"
name = "Tesla (Peak vs Low Timing)"
timing = "peaks"

[strategies.timing_demo2]
ticker = "TSLA"
name = "Tesla (Regular DCA)"
timing = "regular"
```

---

<div align="center">

**🎲 55 Random Strategies • 🤖 Interactive Telegram Bot • ⚡ Timing Analysis**

*From Apple vs Tesla to Bitcoin vs Gold - Discover fascinating investment scenarios*

[⭐ Star this repo](https://github.com/yourusername/reelstonks) • [🤖 Setup Telegram Bot](docs/TELEGRAM.md) • [🎲 View All Strategies](assets/options/random/)

**Made with ❤️ for the investment community** 🚀

</div>