"""
Main entry point for ReelStonks application.

Usage:
    python main.py

    or

    uv run main.py

Requires:
    - Python 3.13+
    - FFmpeg (for video generation)
    - Configuration in config.toml (see config.toml.template for details)

Generates:
    - Timestamped directory in assets/animations/
    - Wealth projection animation in the timestamped directory
"""

import tomllib
import pandas as pd
import time
import os
from src.config import ReelStonksManager
from src.bot import TelegramManager
from src.bot.telegram.interactive_menu import InteractiveMenu
from src.logger import get_logger
from src.data.yahoo_fetcher import get_stock_price_timeseries
from src.animation.time_series_animation import create_wealth_animation
from src.utils.title_constructor import construct_string_title
from src.utils.setup import setup_project
from src.utils.paths import (
    get_config_path,
    get_timestamped_animations_dir,
    get_random_options_dir,
    move_file_to_used_folder,
    get_number_files_from_dir,
    get_random_file_from_dir,
    get_filename_without_extension,
    zip_folder,
)
from src.utils.config_merger import merge_strategy_config, validate_merged_config
from src.bot.tiktok.tiktok import titktok_upload_manager, generate_description

LOGGER = get_logger(__file__)
LOGGER.activate()


def _get_projection(
    config: ReelStonksManager, telegram_manager: TelegramManager = None
) -> pd.DataFrame:

    projections = []
    stock_data_cache = {}  # Cache to avoid re-fetching same ticker data
    last_projection = None  # Keep track of the last successful projection

    # Loop through strategies
    for strategy in config.strategies:
        LOGGER.info(
            f"Processing strategy '{strategy.strategy_id}' for {strategy.ticker_symbol} ..."
        )
        # Check if we already have data for this ticker
        if strategy.ticker_symbol not in stock_data_cache:
            # Fetch stock data for ticker
            stock_data = get_stock_price_timeseries(
                strategy.ticker_symbol,
                interval=config.interval,
                start_date=config.start_date_str,
                end_date=config.today_str,
                save_data=config.save_data,
            )

            if stock_data.empty:
                LOGGER.warning(
                    f"Data not found - skip strategy id={strategy.strategy_id!r}"
                )
                continue

            stock_data_cache[strategy.ticker_symbol] = stock_data
        else:
            stock_data = stock_data_cache[strategy.ticker_symbol]
            LOGGER.info(f" --- Using cached data for {strategy.ticker_symbol}")
        # Get corresponding investment object for this strategy
        if strategy.strategy_id in config.failed_strategies:
            LOGGER.error(
                f"❌ Skipping strategy '{strategy.strategy_id}' due to unsupported investment object."
            )
            continue
        # Send strategy start notification to Telegram
        if telegram_manager:
            telegram_manager.send_message(
                f"Processed ticker: {strategy.ticker_symbol}..."
            )
        # Initialize investment object
        investment_object = config.investment_objects[strategy.strategy_id]
        investment = investment_object(
            df=stock_data,
            investment_amount=config.investment_amount,
            tax_rate=config.tax_rate,
            tax_free_return_threshold_per_annu=config.tax_free_return_threshold_per_annu,
        )
        # Get wealth projection and append to list as series
        projection = investment.invest(
            strategy.reinvest_dividends, config.data_frequency_timestamp
        )
        projection_series = projection["Wealth"].rename(strategy.display_name)
        projections.append(projection_series)
        last_projection = projection  # Keep track of the last successful projection

    # Check if we have any successful projections
    if not projections:
        LOGGER.error("❌ No successful projections created. Cannot create animation.")

        if telegram_manager:
            telegram_manager.send_message(
                "❌ No successful projections created. Cannot create animation."
            )
        exit(3)

    LOGGER.info("💰 Concatenating projections and filtering for investment periods...")
    total_investments = (
        last_projection["Invested Cash"].cumsum().rename("Total Investments")
    )

    projections.append(total_investments)
    projections = pd.concat(projections, axis=1)
    projections = projections.loc[projections["Total Investments"] > 0]

    if telegram_manager:
        telegram_manager.send_message(
            "Investment projection completed. Starting animation..."
        )

    return projections


def _create_animation(
    config: ReelStonksManager, projections: pd.DataFrame, animation_filename: str
) -> None:
    # Construct title only if show_title is enabled
    title = ""
    if config.show_title:
        strategy_names = [strategy.display_name for strategy in config.strategies]
        title = construct_string_title(
            strategy_names,
            config.investment_amount,
            config.years_ago,
            config.interval_title,
        )

    # Create animation
    create_wealth_animation(
        projections,
        config.years_ago,
        filename=str(animation_filename),
        duration_sec=config.animation_duration_seconds,
        fps=config.fps,
        title=title,
        music_filename=config.use_music,
    )

    return title


def _task_random(config_data: dict, task_config: dict) -> None:
    """Executed random task, ie., randomly loads config(s)
    from random options directory. Number of scenarios are specified
    via the `count` key in the `task` section of the config.toml file.

    Example:
        [tasks]
        random = true
        count = 5  # Can be "all" or integer
    """
    LOGGER.info("📈 Running random task from [tasks] section of config.toml")
    run_counts = task_config.get("count", "all")
    zip_results = task_config.get("zip", False)
    random_dir = get_random_options_dir()
    # Get number of all files in random directory
    if run_counts == "all":
        run_counts = get_number_files_from_dir(random_dir, "toml")

    LOGGER.info(f"📈 Running for {run_counts!r} random strategies")
    # Create animation with dynamic path
    animations_dir = get_timestamped_animations_dir()

    # Run for specified number of times
    for _ in range(int(run_counts)):
        file = get_random_file_from_dir(random_dir, "toml")
        with open(file, "rb") as f:
            config_data_random = tomllib.load(f)

        # Merge with base config
        config_data = merge_strategy_config(config_data, config_data_random)
        config = ReelStonksManager(config_dict=config_data)
        # Get projections
        projections = _get_projection(config)

        basefilename = get_filename_without_extension(file)
        animations_dir_sub = animations_dir / basefilename
        # Create directory for animation
        os.makedirs(animations_dir_sub, exist_ok=True)
        animation_filename = animations_dir_sub / "wealth_animation.mp4"
        LOGGER.info(f"📈 Generating description for {basefilename}")
        # Get tiktok description
        tiktok_description = generate_description(config)
        # Save description to file
        with open(animations_dir_sub / "description.txt", "w") as f:
            f.write(tiktok_description)

        LOGGER.info(f"📈 Generate animation for {basefilename}")
        title = _create_animation(config, projections, animation_filename)
        # Move random toml file to `used` folder
        move_file_to_used_folder(file)

        if zip_results:
            zip_folder(
                animations_dir,
                animations_dir.parent / get_filename_without_extension(animations_dir),
            )
            LOGGER.info(f"📈 Zipped results to {animations_dir.parent}")

    LOGGER.info(exit_code := "📈 Random task batch completed. Exiting.")
    exit(exit_code)


def main_task_batch(config_data: dict) -> None:
    """Executes task batches, ie., multiple runs of the main loop.
    This is useful for running multiple scenarios in a row, e.g.,
    for creating a video compilation.

    Supported task batches:
        - Random (see _task_random)
    """
    if task_config := config_data.get("tasks", {}):
        if task_config.get("random", False):
            _task_random(config_data, task_config)


if __name__ == "__main__":
    # Set up project directories first
    LOGGER.info("🚀 Initializing ReelStonks...")
    if not setup_project(verbose=True):
        LOGGER.error("❌ Failed to set up project directories. Exiting.")
        exit(1)

    # Load configuration from TOML
    LOGGER.info("📋 Loading master configuration file...")
    config_path = get_config_path()
    with open(config_path, "rb") as f:
        config_data = tomllib.load(f)

    # Check if we have a task batch to run
    _ = main_task_batch(config_data)

    # Initialize Config with values from the TOML file (temporary for Telegram setup)
    telegram_config = ReelStonksManager._load_telegram_config(config_data)
    telegram_manager = TelegramManager(telegram_config)

    # Main loop for creating videos
    while True:
        # Record start time for duration calculation
        start_time = time.time()

        # Interactive menu system (if enabled and Telegram is configured)
        if telegram_manager.interactive_menu:
            LOGGER.info("🎛️ Starting interactive menu system...")
            interactive_menu = InteractiveMenu(telegram_manager)

            # Show menu and wait for user selection
            selected_config = interactive_menu.show_menu_and_wait(config_data)

            if selected_config:
                # Merge selected configuration with base config
                LOGGER.info("🔄 Merging configuration with user selection")
                config_data = merge_strategy_config(config_data, selected_config)

                # Validate merged configuration
                if not validate_merged_config(config_data):
                    LOGGER.error(
                        "❌ Invalid merged configuration, falling back to default"
                    )
                    # Reload original config
                    with open(config_path, "rb") as f:
                        config_data = tomllib.load(f)
            else:
                LOGGER.info("🔧 Using default configuration from config.toml")
        else:
            if telegram_manager.telegram_enabled:
                LOGGER.info("🔧 Interactive menu disabled, using default configuration")
            else:
                LOGGER.info("🔧 Telegram not enabled, using default configuration")

        # Initialize the final Config with the (potentially merged) configuration
        LOGGER.info("🔧 Initializing ReelStonksManager with final configuration...")
        config = ReelStonksManager(config_dict=config_data)
        projections = _get_projection(config, telegram_manager)

        # Create animation with dynamic path
        animations_dir = get_timestamped_animations_dir()
        animation_filename = animations_dir / "wealth_animation.mp4"
        title = _create_animation(config, projections, animation_filename)

        telegram_manager.send_message("Animation completed. Sending video...")
        telegram_manager.send_video(animation_filename)

        # Calculate total duration and send completion notification
        total_duration = time.time() - start_time

        # Send completion notification
        LOGGER.info(
            f"✅ ReelStonks completed successfully in {total_duration:.2f} seconds"
        )

        telegram_manager.send_message(
            f"✅ ReelStonks completed successfully in {total_duration:.2f} seconds. Send full log report..."
        )

        # Upload video to TikTok
        if config.upload_cfg.get("tiktok", False):
            LOGGER.info(" Uploading video to TikTok...")
            # Pass the same music filename that was used in the animation
            titktok_upload_manager(
                config, str(animation_filename), music_filename=config.use_music
            )
            LOGGER.info("✅ Video uploaded to TikTok!")
            telegram_manager.send_message("✅ Video uploaded to TikTok!")
        # Otherwise, just send the title and description
        else:
            description = generate_description(config)
            telegram_manager.send_message(f"Title to use:\n\n------------------")
            telegram_manager.send_message(title)
            telegram_manager.send_message(f"Description to use:\n\n------------------")
            telegram_manager.send_message(description)

        # Ask if user wants to create another video (only if Telegram is enabled)
        if telegram_manager.telegram_enabled:
            create_another = telegram_manager.ask_create_another_video(
                timeout_seconds=300
            )
            if not create_another:
                LOGGER.info("🏁 User chose to finish. Exiting ReelStonks.")
                break
        else:
            # If Telegram is not enabled, exit after one video
            LOGGER.info("🏁 Telegram not enabled. Exiting after creating one video.")
            break

    LOGGER.info("🏁 ReelStonks session ended.")
